"""
Configuration management for ChatGPT Controller Client
"""

import os
import yaml
from pathlib import Path
from typing import Optional, Dict, Any
from pydantic import BaseModel, Field


class ClientConfig(BaseModel):
    """Client configuration model"""
    
    # API Configuration
    base_url: str = Field(default="http://localhost:8000", description="API base URL")
    api_version: str = Field(default="v1", description="API version")
    timeout: int = Field(default=30, description="Request timeout in seconds")
    
    # Authentication (if needed in future)
    api_key: Optional[str] = Field(default=None, description="API key for authentication")
    
    # CLI Configuration
    cli_output_format: str = Field(default="table", description="Default CLI output format")
    cli_color: bool = Field(default=True, description="Enable colored CLI output")
    
    # GUI Configuration
    gui_theme: str = Field(default="light", description="GUI theme")
    gui_window_size: tuple[int, int] = Field(default=(1200, 800), description="Default window size")
    
    # Logging
    log_level: str = Field(default="INFO", description="Logging level")
    log_file: Optional[str] = Field(default=None, description="Log file path")
    
    @property
    def api_base_url(self) -> str:
        """Get the full API base URL"""
        return f"{self.base_url}/api/{self.api_version}"


class ConfigManager:
    """Configuration manager for the client"""
    
    def __init__(self, config_file: Optional[Path] = None):
        self.config_file = config_file or self._get_default_config_file()
        self._config: Optional[ClientConfig] = None
    
    def _get_default_config_file(self) -> Path:
        """Get the default configuration file path"""
        # Use client directory for config file
        client_dir = Path(__file__).parent.parent
        config_dir = client_dir / "config"
        config_dir.mkdir(parents=True, exist_ok=True)
        return config_dir / "config.yaml"
    
    def load_config(self) -> ClientConfig:
        """Load configuration from file"""
        if self._config is not None:
            return self._config
        
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r') as f:
                    config_data = yaml.safe_load(f) or {}
                self._config = ClientConfig(**config_data)
            except Exception as e:
                print(f"Warning: Failed to load config from {self.config_file}: {e}")
                self._config = ClientConfig()
        else:
            self._config = ClientConfig()
            self.save_config()  # Create default config file
        
        return self._config
    
    def save_config(self) -> None:
        """Save configuration to file"""
        if self._config is None:
            self._config = ClientConfig()
        
        # Ensure config directory exists
        self.config_file.parent.mkdir(parents=True, exist_ok=True)
        
        # Convert to dict and save
        config_dict = self._config.model_dump()
        with open(self.config_file, 'w') as f:
            yaml.dump(config_dict, f, default_flow_style=False, indent=2)
    
    def update_config(self, **kwargs) -> None:
        """Update configuration values"""
        if self._config is None:
            self._config = self.load_config()
        
        # Update the config with new values
        config_dict = self._config.model_dump()
        config_dict.update(kwargs)
        self._config = ClientConfig(**config_dict)
        self.save_config()
    
    @property
    def config(self) -> ClientConfig:
        """Get the current configuration"""
        if self._config is None:
            self._config = self.load_config()
        return self._config


# Global config manager instance
config_manager = ConfigManager()
