{"openapi": "3.1.0", "info": {"title": "ChatGPT Controller API", "description": "REST API for ChatGPT Forward Plugin Controller", "version": "1.0.0"}, "paths": {"/api/v1/status": {"get": {"summary": "Get Status", "description": "Get system status including WebSocket connection and health metrics\n\nReturns:\n    StatusResponse: System status information", "operationId": "get_status_api_v1_status_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StatusResponse"}}}}}}}, "/api/v1/health": {"get": {"summary": "Health Check", "description": "Simple health check endpoint for load balancers and monitoring\n\nReturns:\n    HealthCheckResponse: Basic health status", "operationId": "health_check_api_v1_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HealthCheckResponse"}}}}}}}, "/api/v1/metrics": {"get": {"summary": "Get Metrics", "description": "Get detailed system metrics\n\nReturns:\n    Dict: Detailed system metrics", "operationId": "get_metrics_api_v1_metrics_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/conversations/start": {"post": {"tags": ["对话管理"], "summary": "启动新的ChatGPT对话", "description": "启动一个新的ChatGPT对话会话。\n\n    此端点将：\n    1. 构建带有指定参数的ChatGPT URL\n    2. 通过WebSocket向浏览器扩展发送打开命令\n    3. 等待页面加载并尝试提交初始提示\n    4. 提取对话ID并在数据库中创建记录\n    5. 返回对话详情和访问URL\n\n    **注意事项：**\n    - 需要浏览器扩展连接到WebSocket服务器\n    - 初始提示会被URL编码并作为查询参数传递\n    - 如果页面加载超时，系统会继续执行但可能影响对话创建", "operationId": "start_conversation_api_v1_conversations_start_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConversationStartRequest"}}}, "required": true}, "responses": {"200": {"description": "对话启动成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConversationStartResponse"}, "example": {"success": true, "message": "Conversation started successfully", "conversation_id": "conv-1234567890", "url": "https://chatgpt.com/c/conv-1234567890", "redirect_url": "https://chatgpt.com/c/conv-1234567890", "timestamp": "2024-01-01T12:00:00Z"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"example": {"detail": "Browser control operation 'open_chatgpt' failed: No browser extension connected"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/conversations": {"get": {"tags": ["对话管理"], "summary": "获取对话列表", "description": "获取对话列表，支持多种过滤条件和分页功能。\n\n    **支持的过滤条件：**\n    - `status`: 按对话状态过滤（active, archived, deleted）\n    - `mode`: 按对话模式过滤（research, reason, search, canvas, picture_v2）\n    - `search`: 在对话标题和初始提示中搜索关键词\n    - `created_after`: 获取指定时间之后创建的对话\n    - `created_before`: 获取指定时间之前创建的对话\n\n    **分页参数：**\n    - `page`: 页码（从1开始）\n    - `page_size`: 每页数量（1-100，默认50）\n\n    **排序：**\n    - 按创建时间倒序排列（最新的在前）", "operationId": "list_conversations_api_v1_conversations_get", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "title": "Page"}}, {"name": "page_size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "default": 50, "title": "<PERSON>"}}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/ConversationStatus"}, {"type": "null"}], "title": "Status"}}, {"name": "mode", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/ChatGPTMode"}, {"type": "null"}], "title": "Mode"}}, {"name": "search", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Search"}}, {"name": "created_after", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Created After"}}, {"name": "created_before", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Created Before"}}], "responses": {"200": {"description": "成功获取对话列表", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConversationListResponse"}, "example": {"success": true, "conversations": [{"id": "conv-1234567890", "title": "量子计算讨论", "mode": "research", "init_prompt": "帮我了解量子计算的基本原理", "status": "active", "is_cached": true, "created_at": "2024-01-01T12:00:00Z", "updated_at": "2024-01-01T12:30:00Z", "last_accessed": "2024-01-01T12:30:00Z", "url": "https://chatgpt.com/?q=...", "redirect_url": "https://chatgpt.com/c/conv-1234567890", "message_count": 5}], "total": 1, "page": 1, "page_size": 50, "timestamp": "2024-01-01T12:00:00Z"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"example": {"detail": "Failed to list conversations: Database connection error"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/conversations/{conversation_id}": {"get": {"tags": ["对话管理"], "summary": "获取对话详情", "description": "获取指定对话的详细内容，包括对话信息和所有消息。\n\n    **智能缓存机制：**\n    - 当 `use_cache=true` 且存在缓存数据时，直接返回缓存内容\n    - 当 `use_cache=false` 或无缓存时，从浏览器实时获取最新内容\n    - 实时获取会打开对话页面并等待API监控捕获数据\n\n    **获取流程：**\n    1. 检查数据库中的对话记录\n    2. 根据缓存策略决定数据源\n    3. 如需实时获取，通过WebSocket打开对话页面\n    4. 等待页面加载并捕获API响应\n    5. 返回对话数据和消息列表\n\n    **注意事项：**\n    - 实时获取需要浏览器扩展连接\n    - 可能需要等待几秒钟以获取完整数据", "operationId": "get_conversation_api_v1_conversations__conversation_id__get", "parameters": [{"name": "conversation_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Conversation Id"}}, {"name": "use_cache", "in": "query", "required": false, "schema": {"type": "boolean", "description": "是否使用缓存数据（如果可用）。设为false将强制从浏览器获取最新内容", "default": true, "title": "Use Cache"}, "description": "是否使用缓存数据（如果可用）。设为false将强制从浏览器获取最新内容"}], "responses": {"200": {"description": "成功获取对话详情", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConversationResponse"}, "example": {"success": true, "conversation": {"id": "conv-1234567890", "title": "量子计算讨论", "mode": "research", "init_prompt": "帮我了解量子计算的基本原理", "status": "active", "is_cached": true, "created_at": "2024-01-01T12:00:00Z", "updated_at": "2024-01-01T12:30:00Z", "last_accessed": "2024-01-01T12:30:00Z", "url": "https://chatgpt.com/?q=...", "redirect_url": "https://chatgpt.com/c/conv-1234567890", "message_count": 2}, "messages": [{"id": 1, "conversation_id": "conv-1234567890", "role": "user", "content": "帮我了解量子计算的基本原理", "content_type": "text", "created_at": "2024-01-01T12:00:00Z", "sent_at": "2024-01-01T12:00:00Z", "metadata": {}}, {"id": 2, "conversation_id": "conv-1234567890", "role": "assistant", "content": "量子计算是一种基于量子力学原理的计算方式...", "content_type": "text", "created_at": "2024-01-01T12:01:00Z", "metadata": {}}], "from_cache": true, "timestamp": "2024-01-01T12:00:00Z"}}}}, "404": {"description": "对话不存在", "content": {"application/json": {"example": {"detail": "Conversation conv-1234567890 not found"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"example": {"detail": "Browser control error: No browser extension connected"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/conversations/{conversation_id}/messages": {"post": {"tags": ["消息管理"], "summary": "发送消息到对话", "description": "向指定的对话发送新消息。\n\n    **发送流程：**\n    1. 验证对话是否存在\n    2. 通过WebSocket打开对话页面\n    3. 等待页面加载完成\n    4. 在输入框中输入消息内容\n    5. 点击发送按钮提交消息\n    6. 在数据库中记录用户消息\n    7. 更新对话的最后访问时间\n\n    **技术细节：**\n    - 使用XPath选择器定位输入框和发送按钮\n    - 支持多种可能的页面布局\n    - 自动处理页面加载等待\n    - 记录消息发送时间戳\n\n    **注意事项：**\n    - 需要浏览器扩展连接到WebSocket服务器\n    - 消息长度限制为1-10000字符\n    - 发送后ChatGPT的回复会通过API监控自动捕获", "operationId": "send_message_api_v1_conversations__conversation_id__messages_post", "parameters": [{"name": "conversation_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Conversation Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageRequest"}}}}, "responses": {"200": {"description": "消息发送成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageResponse"}, "example": {"success": true, "message": "Message sent successfully", "message_id": "123", "sent_at": "2024-01-01T12:00:00Z", "timestamp": "2024-01-01T12:00:00Z"}}}}, "404": {"description": "对话不存在", "content": {"application/json": {"example": {"detail": "Conversation conv-1234567890 not found"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"example": {"detail": "Browser control error: Failed to input text in any textarea"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"schemas": {"ChatGPTMode": {"type": "string", "enum": ["research", "reason", "search", "canvas", "picture_v2"], "title": "ChatGPTMode", "description": "ChatGPT conversation modes"}, "ConnectionStatus": {"properties": {"connected": {"type": "boolean", "title": "Connected"}, "client_count": {"type": "integer", "title": "Client Count"}, "last_ping": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Ping"}, "uptime_seconds": {"type": "number", "title": "Uptime Seconds"}}, "type": "object", "required": ["connected", "client_count", "uptime_seconds"], "title": "ConnectionStatus", "description": "WebSocket connection status"}, "ConversationData": {"properties": {"id": {"type": "string", "title": "Id"}, "title": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Title"}, "mode": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Mode"}, "init_prompt": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Init Prompt"}, "status": {"$ref": "#/components/schemas/ConversationStatus"}, "is_cached": {"type": "boolean", "title": "<PERSON>"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "last_accessed": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Accessed"}, "url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Url"}, "redirect_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Redirect Url"}, "message_count": {"type": "integer", "title": "Message Count", "default": 0}}, "type": "object", "required": ["id", "status", "is_cached", "created_at", "updated_at"], "title": "ConversationData", "description": "Conversation data model"}, "ConversationListResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Message"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "conversations": {"items": {"$ref": "#/components/schemas/ConversationData"}, "type": "array", "title": "Conversations"}, "total": {"type": "integer", "title": "Total"}, "page": {"type": "integer", "title": "Page", "default": 1}, "page_size": {"type": "integer", "title": "<PERSON>", "default": 50}}, "type": "object", "required": ["conversations", "total"], "title": "ConversationListResponse", "description": "Response for conversation list"}, "ConversationResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Message"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "conversation": {"$ref": "#/components/schemas/ConversationData"}, "messages": {"items": {"$ref": "#/components/schemas/MessageData"}, "type": "array", "title": "Messages", "default": []}, "cached": {"type": "boolean", "title": "<PERSON><PERSON><PERSON>", "default": false}, "cache_timestamp": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "<PERSON><PERSON>"}}, "type": "object", "required": ["conversation"], "title": "ConversationResponse", "description": "Response for conversation query"}, "ConversationStartRequest": {"properties": {"mode": {"anyOf": [{"$ref": "#/components/schemas/ChatGPTMode"}, {"type": "null"}]}, "init_prompt": {"type": "string", "maxLength": 10000, "minLength": 1, "title": "Init Prompt"}}, "type": "object", "required": ["init_prompt"], "title": "ConversationStartRequest", "description": "Request to start a new conversation", "example": {"init_prompt": "Help me understand quantum computing", "mode": "research"}}, "ConversationStartResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Message"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "conversation_id": {"type": "string", "title": "Conversation Id"}, "url": {"type": "string", "title": "Url"}, "redirect_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Redirect Url"}}, "type": "object", "required": ["conversation_id", "url"], "title": "ConversationStartResponse", "description": "Response for conversation start"}, "ConversationStatus": {"type": "string", "enum": ["active", "archived", "deleted"], "title": "ConversationStatus", "description": "Conversation status values"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "HealthCheckResponse": {"properties": {"status": {"type": "string", "title": "Status", "default": "healthy"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "version": {"type": "string", "title": "Version", "default": "1.0.0"}, "uptime_seconds": {"type": "number", "title": "Uptime Seconds"}, "checks": {"additionalProperties": {"type": "boolean"}, "type": "object", "title": "Checks"}}, "type": "object", "required": ["uptime_seconds", "checks"], "title": "HealthCheckResponse", "description": "Health check response"}, "MessageData": {"properties": {"id": {"type": "integer", "title": "Id"}, "conversation_id": {"type": "string", "title": "Conversation Id"}, "role": {"$ref": "#/components/schemas/MessageRole"}, "content": {"type": "string", "title": "Content"}, "content_type": {"type": "string", "title": "Content Type", "default": "text"}, "message_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Message Id"}, "parent_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Parent Id"}, "model": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Model"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "sent_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "metadata": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}}, "type": "object", "required": ["id", "conversation_id", "role", "content", "created_at"], "title": "MessageData", "description": "Message data model"}, "MessageRequest": {"properties": {"message": {"type": "string", "maxLength": 10000, "minLength": 1, "title": "Message"}}, "type": "object", "required": ["message"], "title": "MessageRequest", "description": "Request to send a message to conversation", "example": {"message": "Can you explain this in more detail?"}}, "MessageResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Message"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "message_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Message Id"}, "sent_at": {"type": "string", "format": "date-time", "title": "<PERSON><PERSON>"}}, "type": "object", "required": ["sent_at"], "title": "MessageResponse", "description": "Response for message sending"}, "MessageRole": {"type": "string", "enum": ["user", "assistant", "system"], "title": "MessageRole", "description": "Message role values"}, "StatusResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Message"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "connection_status": {"$ref": "#/components/schemas/ConnectionStatus"}, "system_health": {"$ref": "#/components/schemas/SystemHealth"}}, "type": "object", "required": ["connection_status", "system_health"], "title": "StatusResponse", "description": "System status response"}, "SystemHealth": {"properties": {"database_healthy": {"type": "boolean", "title": "Database Healthy"}, "websocket_healthy": {"type": "boolean", "title": "Websocket Healthy"}, "memory_usage_mb": {"type": "number", "title": "Memory Usage Mb"}, "cpu_usage_percent": {"type": "number", "title": "Cpu Usage Percent"}}, "type": "object", "required": ["database_healthy", "websocket_healthy", "memory_usage_mb", "cpu_usage_percent"], "title": "SystemHealth", "description": "System health metrics"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}}}