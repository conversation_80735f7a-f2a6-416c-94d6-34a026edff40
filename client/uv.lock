version = 1
requires-python = ">=3.12"

[[package]]
name = "aiohappyeyeballs"
version = "2.6.1"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/26/30/f84a107a9c4331c14b2b586036f40965c128aa4fee4dda5d3d51cb14ad54/aiohappyeyeballs-2.6.1.tar.gz", hash = "sha256:c3f9d0113123803ccadfdf3f0faa505bc78e6a72d1cc4806cbd719826e943558", size = 22760 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/0f/15/5bf3b99495fb160b63f95972b81750f18f7f4e02ad051373b669d17d44f2/aiohappyeyeballs-2.6.1-py3-none-any.whl", hash = "sha256:f349ba8f4b75cb25c99c5c2d84e997e485204d2902a9597802b0371f09331fb8", size = 15265 },
]

[[package]]
name = "aiohttp"
version = "3.12.15"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "aiohappyeyeballs" },
    { name = "aiosignal" },
    { name = "attrs" },
    { name = "frozenlist" },
    { name = "multidict" },
    { name = "propcache" },
    { name = "yarl" },
]
sdist = { url = "https://files.pythonhosted.org/packages/9b/e7/d92a237d8802ca88483906c388f7c201bbe96cd80a165ffd0ac2f6a8d59f/aiohttp-3.12.15.tar.gz", hash = "sha256:4fc61385e9c98d72fcdf47e6dd81833f47b2f77c114c29cd64a361be57a763a2", size = 7823716 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/63/97/77cb2450d9b35f517d6cf506256bf4f5bda3f93a66b4ad64ba7fc917899c/aiohttp-3.12.15-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:802d3868f5776e28f7bf69d349c26fc0efadb81676d0afa88ed00d98a26340b7", size = 702333 },
    { url = "https://files.pythonhosted.org/packages/83/6d/0544e6b08b748682c30b9f65640d006e51f90763b41d7c546693bc22900d/aiohttp-3.12.15-cp312-cp312-macosx_10_13_x86_64.whl", hash = "sha256:f2800614cd560287be05e33a679638e586a2d7401f4ddf99e304d98878c29444", size = 476948 },
    { url = "https://files.pythonhosted.org/packages/3a/1d/c8c40e611e5094330284b1aea8a4b02ca0858f8458614fa35754cab42b9c/aiohttp-3.12.15-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:8466151554b593909d30a0a125d638b4e5f3836e5aecde85b66b80ded1cb5b0d", size = 469787 },
    { url = "https://files.pythonhosted.org/packages/38/7d/b76438e70319796bfff717f325d97ce2e9310f752a267bfdf5192ac6082b/aiohttp-3.12.15-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:2e5a495cb1be69dae4b08f35a6c4579c539e9b5706f606632102c0f855bcba7c", size = 1716590 },
    { url = "https://files.pythonhosted.org/packages/79/b1/60370d70cdf8b269ee1444b390cbd72ce514f0d1cd1a715821c784d272c9/aiohttp-3.12.15-cp312-cp312-manylinux_2_17_armv7l.manylinux2014_armv7l.manylinux_2_31_armv7l.whl", hash = "sha256:6404dfc8cdde35c69aaa489bb3542fb86ef215fc70277c892be8af540e5e21c0", size = 1699241 },
    { url = "https://files.pythonhosted.org/packages/a3/2b/4968a7b8792437ebc12186db31523f541943e99bda8f30335c482bea6879/aiohttp-3.12.15-cp312-cp312-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:3ead1c00f8521a5c9070fcb88f02967b1d8a0544e6d85c253f6968b785e1a2ab", size = 1754335 },
    { url = "https://files.pythonhosted.org/packages/fb/c1/49524ed553f9a0bec1a11fac09e790f49ff669bcd14164f9fab608831c4d/aiohttp-3.12.15-cp312-cp312-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:6990ef617f14450bc6b34941dba4f12d5613cbf4e33805932f853fbd1cf18bfb", size = 1800491 },
    { url = "https://files.pythonhosted.org/packages/de/5e/3bf5acea47a96a28c121b167f5ef659cf71208b19e52a88cdfa5c37f1fcc/aiohttp-3.12.15-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:fd736ed420f4db2b8148b52b46b88ed038d0354255f9a73196b7bbce3ea97545", size = 1719929 },
    { url = "https://files.pythonhosted.org/packages/39/94/8ae30b806835bcd1cba799ba35347dee6961a11bd507db634516210e91d8/aiohttp-3.12.15-cp312-cp312-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:3c5092ce14361a73086b90c6efb3948ffa5be2f5b6fbcf52e8d8c8b8848bb97c", size = 1635733 },
    { url = "https://files.pythonhosted.org/packages/7a/46/06cdef71dd03acd9da7f51ab3a9107318aee12ad38d273f654e4f981583a/aiohttp-3.12.15-cp312-cp312-musllinux_1_2_aarch64.whl", hash = "sha256:aaa2234bb60c4dbf82893e934d8ee8dea30446f0647e024074237a56a08c01bd", size = 1696790 },
    { url = "https://files.pythonhosted.org/packages/02/90/6b4cfaaf92ed98d0ec4d173e78b99b4b1a7551250be8937d9d67ecb356b4/aiohttp-3.12.15-cp312-cp312-musllinux_1_2_armv7l.whl", hash = "sha256:6d86a2fbdd14192e2f234a92d3b494dd4457e683ba07e5905a0b3ee25389ac9f", size = 1718245 },
    { url = "https://files.pythonhosted.org/packages/2e/e6/2593751670fa06f080a846f37f112cbe6f873ba510d070136a6ed46117c6/aiohttp-3.12.15-cp312-cp312-musllinux_1_2_i686.whl", hash = "sha256:a041e7e2612041a6ddf1c6a33b883be6a421247c7afd47e885969ee4cc58bd8d", size = 1658899 },
    { url = "https://files.pythonhosted.org/packages/8f/28/c15bacbdb8b8eb5bf39b10680d129ea7410b859e379b03190f02fa104ffd/aiohttp-3.12.15-cp312-cp312-musllinux_1_2_ppc64le.whl", hash = "sha256:5015082477abeafad7203757ae44299a610e89ee82a1503e3d4184e6bafdd519", size = 1738459 },
    { url = "https://files.pythonhosted.org/packages/00/de/c269cbc4faa01fb10f143b1670633a8ddd5b2e1ffd0548f7aa49cb5c70e2/aiohttp-3.12.15-cp312-cp312-musllinux_1_2_s390x.whl", hash = "sha256:56822ff5ddfd1b745534e658faba944012346184fbfe732e0d6134b744516eea", size = 1766434 },
    { url = "https://files.pythonhosted.org/packages/52/b0/4ff3abd81aa7d929b27d2e1403722a65fc87b763e3a97b3a2a494bfc63bc/aiohttp-3.12.15-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:b2acbbfff69019d9014508c4ba0401822e8bae5a5fdc3b6814285b71231b60f3", size = 1726045 },
    { url = "https://files.pythonhosted.org/packages/71/16/949225a6a2dd6efcbd855fbd90cf476052e648fb011aa538e3b15b89a57a/aiohttp-3.12.15-cp312-cp312-win32.whl", hash = "sha256:d849b0901b50f2185874b9a232f38e26b9b3d4810095a7572eacea939132d4e1", size = 423591 },
    { url = "https://files.pythonhosted.org/packages/2b/d8/fa65d2a349fe938b76d309db1a56a75c4fb8cc7b17a398b698488a939903/aiohttp-3.12.15-cp312-cp312-win_amd64.whl", hash = "sha256:b390ef5f62bb508a9d67cb3bba9b8356e23b3996da7062f1a57ce1a79d2b3d34", size = 450266 },
    { url = "https://files.pythonhosted.org/packages/f2/33/918091abcf102e39d15aba2476ad9e7bd35ddb190dcdd43a854000d3da0d/aiohttp-3.12.15-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:9f922ffd05034d439dde1c77a20461cf4a1b0831e6caa26151fe7aa8aaebc315", size = 696741 },
    { url = "https://files.pythonhosted.org/packages/b5/2a/7495a81e39a998e400f3ecdd44a62107254803d1681d9189be5c2e4530cd/aiohttp-3.12.15-cp313-cp313-macosx_10_13_x86_64.whl", hash = "sha256:2ee8a8ac39ce45f3e55663891d4b1d15598c157b4d494a4613e704c8b43112cd", size = 474407 },
    { url = "https://files.pythonhosted.org/packages/49/fc/a9576ab4be2dcbd0f73ee8675d16c707cfc12d5ee80ccf4015ba543480c9/aiohttp-3.12.15-cp313-cp313-macosx_11_0_arm64.whl", hash = "sha256:3eae49032c29d356b94eee45a3f39fdf4b0814b397638c2f718e96cfadf4c4e4", size = 466703 },
    { url = "https://files.pythonhosted.org/packages/09/2f/d4bcc8448cf536b2b54eed48f19682031ad182faa3a3fee54ebe5b156387/aiohttp-3.12.15-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:b97752ff12cc12f46a9b20327104448042fce5c33a624f88c18f66f9368091c7", size = 1705532 },
    { url = "https://files.pythonhosted.org/packages/f1/f3/59406396083f8b489261e3c011aa8aee9df360a96ac8fa5c2e7e1b8f0466/aiohttp-3.12.15-cp313-cp313-manylinux_2_17_armv7l.manylinux2014_armv7l.manylinux_2_31_armv7l.whl", hash = "sha256:894261472691d6fe76ebb7fcf2e5870a2ac284c7406ddc95823c8598a1390f0d", size = 1686794 },
    { url = "https://files.pythonhosted.org/packages/dc/71/164d194993a8d114ee5656c3b7ae9c12ceee7040d076bf7b32fb98a8c5c6/aiohttp-3.12.15-cp313-cp313-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:5fa5d9eb82ce98959fc1031c28198b431b4d9396894f385cb63f1e2f3f20ca6b", size = 1738865 },
    { url = "https://files.pythonhosted.org/packages/1c/00/d198461b699188a93ead39cb458554d9f0f69879b95078dce416d3209b54/aiohttp-3.12.15-cp313-cp313-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:f0fa751efb11a541f57db59c1dd821bec09031e01452b2b6217319b3a1f34f3d", size = 1788238 },
    { url = "https://files.pythonhosted.org/packages/85/b8/9e7175e1fa0ac8e56baa83bf3c214823ce250d0028955dfb23f43d5e61fd/aiohttp-3.12.15-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:5346b93e62ab51ee2a9d68e8f73c7cf96ffb73568a23e683f931e52450e4148d", size = 1710566 },
    { url = "https://files.pythonhosted.org/packages/59/e4/16a8eac9df39b48ae102ec030fa9f726d3570732e46ba0c592aeeb507b93/aiohttp-3.12.15-cp313-cp313-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:049ec0360f939cd164ecbfd2873eaa432613d5e77d6b04535e3d1fbae5a9e645", size = 1624270 },
    { url = "https://files.pythonhosted.org/packages/1f/f8/cd84dee7b6ace0740908fd0af170f9fab50c2a41ccbc3806aabcb1050141/aiohttp-3.12.15-cp313-cp313-musllinux_1_2_aarch64.whl", hash = "sha256:b52dcf013b57464b6d1e51b627adfd69a8053e84b7103a7cd49c030f9ca44461", size = 1677294 },
    { url = "https://files.pythonhosted.org/packages/ce/42/d0f1f85e50d401eccd12bf85c46ba84f947a84839c8a1c2c5f6e8ab1eb50/aiohttp-3.12.15-cp313-cp313-musllinux_1_2_armv7l.whl", hash = "sha256:9b2af240143dd2765e0fb661fd0361a1b469cab235039ea57663cda087250ea9", size = 1708958 },
    { url = "https://files.pythonhosted.org/packages/d5/6b/f6fa6c5790fb602538483aa5a1b86fcbad66244997e5230d88f9412ef24c/aiohttp-3.12.15-cp313-cp313-musllinux_1_2_i686.whl", hash = "sha256:ac77f709a2cde2cc71257ab2d8c74dd157c67a0558a0d2799d5d571b4c63d44d", size = 1651553 },
    { url = "https://files.pythonhosted.org/packages/04/36/a6d36ad545fa12e61d11d1932eef273928b0495e6a576eb2af04297fdd3c/aiohttp-3.12.15-cp313-cp313-musllinux_1_2_ppc64le.whl", hash = "sha256:47f6b962246f0a774fbd3b6b7be25d59b06fdb2f164cf2513097998fc6a29693", size = 1727688 },
    { url = "https://files.pythonhosted.org/packages/aa/c8/f195e5e06608a97a4e52c5d41c7927301bf757a8e8bb5bbf8cef6c314961/aiohttp-3.12.15-cp313-cp313-musllinux_1_2_s390x.whl", hash = "sha256:760fb7db442f284996e39cf9915a94492e1896baac44f06ae551974907922b64", size = 1761157 },
    { url = "https://files.pythonhosted.org/packages/05/6a/ea199e61b67f25ba688d3ce93f63b49b0a4e3b3d380f03971b4646412fc6/aiohttp-3.12.15-cp313-cp313-musllinux_1_2_x86_64.whl", hash = "sha256:ad702e57dc385cae679c39d318def49aef754455f237499d5b99bea4ef582e51", size = 1710050 },
    { url = "https://files.pythonhosted.org/packages/b4/2e/ffeb7f6256b33635c29dbed29a22a723ff2dd7401fff42ea60cf2060abfb/aiohttp-3.12.15-cp313-cp313-win32.whl", hash = "sha256:f813c3e9032331024de2eb2e32a88d86afb69291fbc37a3a3ae81cc9917fb3d0", size = 422647 },
    { url = "https://files.pythonhosted.org/packages/1b/8e/78ee35774201f38d5e1ba079c9958f7629b1fd079459aea9467441dbfbf5/aiohttp-3.12.15-cp313-cp313-win_amd64.whl", hash = "sha256:1a649001580bdb37c6fdb1bebbd7e3bc688e8ec2b5c6f52edbb664662b17dc84", size = 449067 },
]

[[package]]
name = "aiosignal"
version = "1.4.0"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "frozenlist" },
    { name = "typing-extensions", marker = "python_full_version < '3.13'" },
]
sdist = { url = "https://files.pythonhosted.org/packages/61/62/06741b579156360248d1ec624842ad0edf697050bbaf7c3e46394e106ad1/aiosignal-1.4.0.tar.gz", hash = "sha256:f47eecd9468083c2029cc99945502cb7708b082c232f9aca65da147157b251c7", size = 25007 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/fb/76/641ae371508676492379f16e2fa48f4e2c11741bd63c48be4b12a6b09cba/aiosignal-1.4.0-py3-none-any.whl", hash = "sha256:053243f8b92b990551949e63930a839ff0cf0b0ebbe0597b0f3fb19e1a0fe82e", size = 7490 },
]

[[package]]
name = "annotated-types"
version = "0.7.0"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/ee/67/531ea369ba64dcff5ec9c3402f9f51bf748cec26dde048a2f973a4eea7f5/annotated_types-0.7.0.tar.gz", hash = "sha256:aff07c09a53a08bc8cfccb9c85b05f1aa9a2a6f23728d790723543408344ce89", size = 16081 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/78/b6/6307fbef88d9b5ee7421e68d78a9f162e0da4900bc5f5793f6d3d0e34fb8/annotated_types-0.7.0-py3-none-any.whl", hash = "sha256:1f02e8b43a8fbbc3f3e0d4f0f4bfc8131bcb4eebe8849b8e5c773f3a1c582a53", size = 13643 },
]

[[package]]
name = "anyio"
version = "4.9.0"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "idna" },
    { name = "sniffio" },
    { name = "typing-extensions", marker = "python_full_version < '3.13'" },
]
sdist = { url = "https://files.pythonhosted.org/packages/95/7d/4c1bd541d4dffa1b52bd83fb8527089e097a106fc90b467a7313b105f840/anyio-4.9.0.tar.gz", hash = "sha256:673c0c244e15788651a4ff38710fea9675823028a6f08a5eda409e0c9840a028", size = 190949 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/a1/ee/48ca1a7c89ffec8b6a0c5d02b89c305671d5ffd8d3c94acf8b8c408575bb/anyio-4.9.0-py3-none-any.whl", hash = "sha256:9f76d541cad6e36af7beb62e978876f3b41e3e04f2c1fbf0884604c0a9c4d93c", size = 100916 },
]

[[package]]
name = "attrs"
version = "25.3.0"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/5a/b0/1367933a8532ee6ff8d63537de4f1177af4bff9f3e829baf7331f595bb24/attrs-25.3.0.tar.gz", hash = "sha256:75d7cefc7fb576747b2c81b4442d4d4a1ce0900973527c011d1030fd3bf4af1b", size = 812032 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/77/06/bb80f5f86020c4551da315d78b3ab75e8228f89f0162f2c3a819e407941a/attrs-25.3.0-py3-none-any.whl", hash = "sha256:427318ce031701fea540783410126f03899a97ffc6f61596ad581ac2e40e3bc3", size = 63815 },
]

[[package]]
name = "black"
version = "25.1.0"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "click" },
    { name = "mypy-extensions" },
    { name = "packaging" },
    { name = "pathspec" },
    { name = "platformdirs" },
]
sdist = { url = "https://files.pythonhosted.org/packages/94/49/26a7b0f3f35da4b5a65f081943b7bcd22d7002f5f0fb8098ec1ff21cb6ef/black-25.1.0.tar.gz", hash = "sha256:33496d5cd1222ad73391352b4ae8da15253c5de89b93a80b3e2c8d9a19ec2666", size = 649449 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/83/71/3fe4741df7adf015ad8dfa082dd36c94ca86bb21f25608eb247b4afb15b2/black-25.1.0-cp312-cp312-macosx_10_13_x86_64.whl", hash = "sha256:4b60580e829091e6f9238c848ea6750efed72140b91b048770b64e74fe04908b", size = 1650988 },
    { url = "https://files.pythonhosted.org/packages/13/f3/89aac8a83d73937ccd39bbe8fc6ac8860c11cfa0af5b1c96d081facac844/black-25.1.0-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:1e2978f6df243b155ef5fa7e558a43037c3079093ed5d10fd84c43900f2d8ecc", size = 1453985 },
    { url = "https://files.pythonhosted.org/packages/6f/22/b99efca33f1f3a1d2552c714b1e1b5ae92efac6c43e790ad539a163d1754/black-25.1.0-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:3b48735872ec535027d979e8dcb20bf4f70b5ac75a8ea99f127c106a7d7aba9f", size = 1783816 },
    { url = "https://files.pythonhosted.org/packages/18/7e/a27c3ad3822b6f2e0e00d63d58ff6299a99a5b3aee69fa77cd4b0076b261/black-25.1.0-cp312-cp312-win_amd64.whl", hash = "sha256:ea0213189960bda9cf99be5b8c8ce66bb054af5e9e861249cd23471bd7b0b3ba", size = 1440860 },
    { url = "https://files.pythonhosted.org/packages/98/87/0edf98916640efa5d0696e1abb0a8357b52e69e82322628f25bf14d263d1/black-25.1.0-cp313-cp313-macosx_10_13_x86_64.whl", hash = "sha256:8f0b18a02996a836cc9c9c78e5babec10930862827b1b724ddfe98ccf2f2fe4f", size = 1650673 },
    { url = "https://files.pythonhosted.org/packages/52/e5/f7bf17207cf87fa6e9b676576749c6b6ed0d70f179a3d812c997870291c3/black-25.1.0-cp313-cp313-macosx_11_0_arm64.whl", hash = "sha256:afebb7098bfbc70037a053b91ae8437c3857482d3a690fefc03e9ff7aa9a5fd3", size = 1453190 },
    { url = "https://files.pythonhosted.org/packages/e3/ee/adda3d46d4a9120772fae6de454c8495603c37c4c3b9c60f25b1ab6401fe/black-25.1.0-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:030b9759066a4ee5e5aca28c3c77f9c64789cdd4de8ac1df642c40b708be6171", size = 1782926 },
    { url = "https://files.pythonhosted.org/packages/cc/64/94eb5f45dcb997d2082f097a3944cfc7fe87e071907f677e80788a2d7b7a/black-25.1.0-cp313-cp313-win_amd64.whl", hash = "sha256:a22f402b410566e2d1c950708c77ebf5ebd5d0d88a6a2e87c86d9fb48afa0d18", size = 1442613 },
    { url = "https://files.pythonhosted.org/packages/09/71/54e999902aed72baf26bca0d50781b01838251a462612966e9fc4891eadd/black-25.1.0-py3-none-any.whl", hash = "sha256:95e8176dae143ba9097f351d174fdaf0ccd29efb414b362ae3fd72bf0f710717", size = 207646 },
]

[[package]]
name = "certifi"
version = "2025.7.14"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/b3/76/52c535bcebe74590f296d6c77c86dabf761c41980e1347a2422e4aa2ae41/certifi-2025.7.14.tar.gz", hash = "sha256:8ea99dbdfaaf2ba2f9bac77b9249ef62ec5218e7c2b2e903378ed5fccf765995", size = 163981 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/4f/52/34c6cf5bb9285074dc3531c437b3919e825d976fde097a7a73f79e726d03/certifi-2025.7.14-py3-none-any.whl", hash = "sha256:6b31f564a415d79ee77df69d757bb49a5bb53bd9f756cbbe24394ffd6fc1f4b2", size = 162722 },
]

[[package]]
name = "charset-normalizer"
version = "3.4.2"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/e4/33/89c2ced2b67d1c2a61c19c6751aa8902d46ce3dacb23600a283619f5a12d/charset_normalizer-3.4.2.tar.gz", hash = "sha256:5baececa9ecba31eff645232d59845c07aa030f0c81ee70184a90d35099a0e63", size = 126367 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/d7/a4/37f4d6035c89cac7930395a35cc0f1b872e652eaafb76a6075943754f095/charset_normalizer-3.4.2-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:0c29de6a1a95f24b9a1aa7aefd27d2487263f00dfd55a77719b530788f75cff7", size = 199936 },
    { url = "https://files.pythonhosted.org/packages/ee/8a/1a5e33b73e0d9287274f899d967907cd0bf9c343e651755d9307e0dbf2b3/charset_normalizer-3.4.2-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:cddf7bd982eaa998934a91f69d182aec997c6c468898efe6679af88283b498d3", size = 143790 },
    { url = "https://files.pythonhosted.org/packages/66/52/59521f1d8e6ab1482164fa21409c5ef44da3e9f653c13ba71becdd98dec3/charset_normalizer-3.4.2-cp312-cp312-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:fcbe676a55d7445b22c10967bceaaf0ee69407fbe0ece4d032b6eb8d4565982a", size = 153924 },
    { url = "https://files.pythonhosted.org/packages/86/2d/fb55fdf41964ec782febbf33cb64be480a6b8f16ded2dbe8db27a405c09f/charset_normalizer-3.4.2-cp312-cp312-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:d41c4d287cfc69060fa91cae9683eacffad989f1a10811995fa309df656ec214", size = 146626 },
    { url = "https://files.pythonhosted.org/packages/8c/73/6ede2ec59bce19b3edf4209d70004253ec5f4e319f9a2e3f2f15601ed5f7/charset_normalizer-3.4.2-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:4e594135de17ab3866138f496755f302b72157d115086d100c3f19370839dd3a", size = 148567 },
    { url = "https://files.pythonhosted.org/packages/09/14/957d03c6dc343c04904530b6bef4e5efae5ec7d7990a7cbb868e4595ee30/charset_normalizer-3.4.2-cp312-cp312-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:cf713fe9a71ef6fd5adf7a79670135081cd4431c2943864757f0fa3a65b1fafd", size = 150957 },
    { url = "https://files.pythonhosted.org/packages/0d/c8/8174d0e5c10ccebdcb1b53cc959591c4c722a3ad92461a273e86b9f5a302/charset_normalizer-3.4.2-cp312-cp312-musllinux_1_2_aarch64.whl", hash = "sha256:a370b3e078e418187da8c3674eddb9d983ec09445c99a3a263c2011993522981", size = 145408 },
    { url = "https://files.pythonhosted.org/packages/58/aa/8904b84bc8084ac19dc52feb4f5952c6df03ffb460a887b42615ee1382e8/charset_normalizer-3.4.2-cp312-cp312-musllinux_1_2_i686.whl", hash = "sha256:a955b438e62efdf7e0b7b52a64dc5c3396e2634baa62471768a64bc2adb73d5c", size = 153399 },
    { url = "https://files.pythonhosted.org/packages/c2/26/89ee1f0e264d201cb65cf054aca6038c03b1a0c6b4ae998070392a3ce605/charset_normalizer-3.4.2-cp312-cp312-musllinux_1_2_ppc64le.whl", hash = "sha256:7222ffd5e4de8e57e03ce2cef95a4c43c98fcb72ad86909abdfc2c17d227fc1b", size = 156815 },
    { url = "https://files.pythonhosted.org/packages/fd/07/68e95b4b345bad3dbbd3a8681737b4338ff2c9df29856a6d6d23ac4c73cb/charset_normalizer-3.4.2-cp312-cp312-musllinux_1_2_s390x.whl", hash = "sha256:bee093bf902e1d8fc0ac143c88902c3dfc8941f7ea1d6a8dd2bcb786d33db03d", size = 154537 },
    { url = "https://files.pythonhosted.org/packages/77/1a/5eefc0ce04affb98af07bc05f3bac9094513c0e23b0562d64af46a06aae4/charset_normalizer-3.4.2-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:dedb8adb91d11846ee08bec4c8236c8549ac721c245678282dcb06b221aab59f", size = 149565 },
    { url = "https://files.pythonhosted.org/packages/37/a0/2410e5e6032a174c95e0806b1a6585eb21e12f445ebe239fac441995226a/charset_normalizer-3.4.2-cp312-cp312-win32.whl", hash = "sha256:db4c7bf0e07fc3b7d89ac2a5880a6a8062056801b83ff56d8464b70f65482b6c", size = 98357 },
    { url = "https://files.pythonhosted.org/packages/6c/4f/c02d5c493967af3eda9c771ad4d2bbc8df6f99ddbeb37ceea6e8716a32bc/charset_normalizer-3.4.2-cp312-cp312-win_amd64.whl", hash = "sha256:5a9979887252a82fefd3d3ed2a8e3b937a7a809f65dcb1e068b090e165bbe99e", size = 105776 },
    { url = "https://files.pythonhosted.org/packages/ea/12/a93df3366ed32db1d907d7593a94f1fe6293903e3e92967bebd6950ed12c/charset_normalizer-3.4.2-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:926ca93accd5d36ccdabd803392ddc3e03e6d4cd1cf17deff3b989ab8e9dbcf0", size = 199622 },
    { url = "https://files.pythonhosted.org/packages/04/93/bf204e6f344c39d9937d3c13c8cd5bbfc266472e51fc8c07cb7f64fcd2de/charset_normalizer-3.4.2-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:eba9904b0f38a143592d9fc0e19e2df0fa2e41c3c3745554761c5f6447eedabf", size = 143435 },
    { url = "https://files.pythonhosted.org/packages/22/2a/ea8a2095b0bafa6c5b5a55ffdc2f924455233ee7b91c69b7edfcc9e02284/charset_normalizer-3.4.2-cp313-cp313-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:3fddb7e2c84ac87ac3a947cb4e66d143ca5863ef48e4a5ecb83bd48619e4634e", size = 153653 },
    { url = "https://files.pythonhosted.org/packages/b6/57/1b090ff183d13cef485dfbe272e2fe57622a76694061353c59da52c9a659/charset_normalizer-3.4.2-cp313-cp313-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:98f862da73774290f251b9df8d11161b6cf25b599a66baf087c1ffe340e9bfd1", size = 146231 },
    { url = "https://files.pythonhosted.org/packages/e2/28/ffc026b26f441fc67bd21ab7f03b313ab3fe46714a14b516f931abe1a2d8/charset_normalizer-3.4.2-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:6c9379d65defcab82d07b2a9dfbfc2e95bc8fe0ebb1b176a3190230a3ef0e07c", size = 148243 },
    { url = "https://files.pythonhosted.org/packages/c0/0f/9abe9bd191629c33e69e47c6ef45ef99773320e9ad8e9cb08b8ab4a8d4cb/charset_normalizer-3.4.2-cp313-cp313-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:e635b87f01ebc977342e2697d05b56632f5f879a4f15955dfe8cef2448b51691", size = 150442 },
    { url = "https://files.pythonhosted.org/packages/67/7c/a123bbcedca91d5916c056407f89a7f5e8fdfce12ba825d7d6b9954a1a3c/charset_normalizer-3.4.2-cp313-cp313-musllinux_1_2_aarch64.whl", hash = "sha256:1c95a1e2902a8b722868587c0e1184ad5c55631de5afc0eb96bc4b0d738092c0", size = 145147 },
    { url = "https://files.pythonhosted.org/packages/ec/fe/1ac556fa4899d967b83e9893788e86b6af4d83e4726511eaaad035e36595/charset_normalizer-3.4.2-cp313-cp313-musllinux_1_2_i686.whl", hash = "sha256:ef8de666d6179b009dce7bcb2ad4c4a779f113f12caf8dc77f0162c29d20490b", size = 153057 },
    { url = "https://files.pythonhosted.org/packages/2b/ff/acfc0b0a70b19e3e54febdd5301a98b72fa07635e56f24f60502e954c461/charset_normalizer-3.4.2-cp313-cp313-musllinux_1_2_ppc64le.whl", hash = "sha256:32fc0341d72e0f73f80acb0a2c94216bd704f4f0bce10aedea38f30502b271ff", size = 156454 },
    { url = "https://files.pythonhosted.org/packages/92/08/95b458ce9c740d0645feb0e96cea1f5ec946ea9c580a94adfe0b617f3573/charset_normalizer-3.4.2-cp313-cp313-musllinux_1_2_s390x.whl", hash = "sha256:289200a18fa698949d2b39c671c2cc7a24d44096784e76614899a7ccf2574b7b", size = 154174 },
    { url = "https://files.pythonhosted.org/packages/78/be/8392efc43487ac051eee6c36d5fbd63032d78f7728cb37aebcc98191f1ff/charset_normalizer-3.4.2-cp313-cp313-musllinux_1_2_x86_64.whl", hash = "sha256:4a476b06fbcf359ad25d34a057b7219281286ae2477cc5ff5e3f70a246971148", size = 149166 },
    { url = "https://files.pythonhosted.org/packages/44/96/392abd49b094d30b91d9fbda6a69519e95802250b777841cf3bda8fe136c/charset_normalizer-3.4.2-cp313-cp313-win32.whl", hash = "sha256:aaeeb6a479c7667fbe1099af9617c83aaca22182d6cf8c53966491a0f1b7ffb7", size = 98064 },
    { url = "https://files.pythonhosted.org/packages/e9/b0/0200da600134e001d91851ddc797809e2fe0ea72de90e09bec5a2fbdaccb/charset_normalizer-3.4.2-cp313-cp313-win_amd64.whl", hash = "sha256:aa6af9e7d59f9c12b33ae4e9450619cf2488e2bbe9b44030905877f0b2324980", size = 105641 },
    { url = "https://files.pythonhosted.org/packages/20/94/c5790835a017658cbfabd07f3bfb549140c3ac458cfc196323996b10095a/charset_normalizer-3.4.2-py3-none-any.whl", hash = "sha256:7f56930ab0abd1c45cd15be65cc741c28b1c9a34876ce8c17a2fa107810c0af0", size = 52626 },
]

[[package]]
name = "chatgpt-controller-client"
version = "0.1.0"
source = { editable = "." }
dependencies = [
    { name = "aiohttp" },
    { name = "httpx" },
    { name = "keyboard" },
    { name = "pydantic" },
    { name = "pyqt6" },
    { name = "python-dateutil" },
    { name = "pyyaml" },
    { name = "requests" },
    { name = "rich" },
    { name = "typer" },
]

[package.optional-dependencies]
dev = [
    { name = "black" },
    { name = "isort" },
    { name = "mypy" },
    { name = "pytest" },
    { name = "pytest-asyncio" },
    { name = "pytest-mock" },
]

[package.metadata]
requires-dist = [
    { name = "aiohttp", specifier = ">=3.12.15" },
    { name = "black", marker = "extra == 'dev'", specifier = ">=23.0.0" },
    { name = "httpx", specifier = ">=0.25.0" },
    { name = "isort", marker = "extra == 'dev'", specifier = ">=5.12.0" },
    { name = "keyboard", specifier = ">=0.13.5" },
    { name = "mypy", marker = "extra == 'dev'", specifier = ">=1.0.0" },
    { name = "pydantic", specifier = ">=2.0.0" },
    { name = "pyqt6", specifier = ">=6.5.0" },
    { name = "pytest", marker = "extra == 'dev'", specifier = ">=7.0.0" },
    { name = "pytest-asyncio", marker = "extra == 'dev'", specifier = ">=0.21.0" },
    { name = "pytest-mock", marker = "extra == 'dev'", specifier = ">=3.10.0" },
    { name = "python-dateutil", specifier = ">=2.8.0" },
    { name = "pyyaml", specifier = ">=6.0" },
    { name = "requests", specifier = ">=2.32.4" },
    { name = "rich", specifier = ">=13.0.0" },
    { name = "typer", specifier = ">=0.9.0" },
]

[[package]]
name = "click"
version = "8.2.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "colorama", marker = "platform_system == 'Windows'" },
]
sdist = { url = "https://files.pythonhosted.org/packages/60/6c/8ca2efa64cf75a977a0d7fac081354553ebe483345c734fb6b6515d96bbc/click-8.2.1.tar.gz", hash = "sha256:27c491cc05d968d271d5a1db13e3b5a184636d9d930f148c50b038f0d0646202", size = 286342 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/85/32/10bb5764d90a8eee674e9dc6f4db6a0ab47c8c4d0d83c27f7c39ac415a4d/click-8.2.1-py3-none-any.whl", hash = "sha256:61a3265b914e850b85317d0b3109c7f8cd35a670f963866005d6ef1d5175a12b", size = 102215 },
]

[[package]]
name = "colorama"
version = "0.4.6"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/d8/53/6f443c9a4a8358a93a6792e2acffb9d9d5cb0a5cfd8802644b7b1c9a02e4/colorama-0.4.6.tar.gz", hash = "sha256:08695f5cb7ed6e0531a20572697297273c47b8cae5a63ffc6d6ed5c201be6e44", size = 27697 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/d1/d6/3965ed04c63042e047cb6a3e6ed1a63a35087b6a609aa3a15ed8ac56c221/colorama-0.4.6-py2.py3-none-any.whl", hash = "sha256:4f1d9991f5acc0ca119f9d443620b77f9d6b33703e51011c16baf57afb285fc6", size = 25335 },
]

[[package]]
name = "frozenlist"
version = "1.7.0"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/79/b1/b64018016eeb087db503b038296fd782586432b9c077fc5c7839e9cb6ef6/frozenlist-1.7.0.tar.gz", hash = "sha256:2e310d81923c2437ea8670467121cc3e9b0f76d3043cc1d2331d56c7fb7a3a8f", size = 45078 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/ef/a2/c8131383f1e66adad5f6ecfcce383d584ca94055a34d683bbb24ac5f2f1c/frozenlist-1.7.0-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:3dbf9952c4bb0e90e98aec1bd992b3318685005702656bc6f67c1a32b76787f2", size = 81424 },
    { url = "https://files.pythonhosted.org/packages/4c/9d/02754159955088cb52567337d1113f945b9e444c4960771ea90eb73de8db/frozenlist-1.7.0-cp312-cp312-macosx_10_13_x86_64.whl", hash = "sha256:1f5906d3359300b8a9bb194239491122e6cf1444c2efb88865426f170c262cdb", size = 47952 },
    { url = "https://files.pythonhosted.org/packages/01/7a/0046ef1bd6699b40acd2067ed6d6670b4db2f425c56980fa21c982c2a9db/frozenlist-1.7.0-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:3dabd5a8f84573c8d10d8859a50ea2dec01eea372031929871368c09fa103478", size = 46688 },
    { url = "https://files.pythonhosted.org/packages/d6/a2/a910bafe29c86997363fb4c02069df4ff0b5bc39d33c5198b4e9dd42d8f8/frozenlist-1.7.0-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:aa57daa5917f1738064f302bf2626281a1cb01920c32f711fbc7bc36111058a8", size = 243084 },
    { url = "https://files.pythonhosted.org/packages/64/3e/5036af9d5031374c64c387469bfcc3af537fc0f5b1187d83a1cf6fab1639/frozenlist-1.7.0-cp312-cp312-manylinux_2_17_armv7l.manylinux2014_armv7l.manylinux_2_31_armv7l.whl", hash = "sha256:c193dda2b6d49f4c4398962810fa7d7c78f032bf45572b3e04dd5249dff27e08", size = 233524 },
    { url = "https://files.pythonhosted.org/packages/06/39/6a17b7c107a2887e781a48ecf20ad20f1c39d94b2a548c83615b5b879f28/frozenlist-1.7.0-cp312-cp312-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:bfe2b675cf0aaa6d61bf8fbffd3c274b3c9b7b1623beb3809df8a81399a4a9c4", size = 248493 },
    { url = "https://files.pythonhosted.org/packages/be/00/711d1337c7327d88c44d91dd0f556a1c47fb99afc060ae0ef66b4d24793d/frozenlist-1.7.0-cp312-cp312-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:8fc5d5cda37f62b262405cf9652cf0856839c4be8ee41be0afe8858f17f4c94b", size = 244116 },
    { url = "https://files.pythonhosted.org/packages/24/fe/74e6ec0639c115df13d5850e75722750adabdc7de24e37e05a40527ca539/frozenlist-1.7.0-cp312-cp312-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:b0d5ce521d1dd7d620198829b87ea002956e4319002ef0bc8d3e6d045cb4646e", size = 224557 },
    { url = "https://files.pythonhosted.org/packages/8d/db/48421f62a6f77c553575201e89048e97198046b793f4a089c79a6e3268bd/frozenlist-1.7.0-cp312-cp312-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:488d0a7d6a0008ca0db273c542098a0fa9e7dfaa7e57f70acef43f32b3f69dca", size = 241820 },
    { url = "https://files.pythonhosted.org/packages/1d/fa/cb4a76bea23047c8462976ea7b7a2bf53997a0ca171302deae9d6dd12096/frozenlist-1.7.0-cp312-cp312-musllinux_1_2_aarch64.whl", hash = "sha256:15a7eaba63983d22c54d255b854e8108e7e5f3e89f647fc854bd77a237e767df", size = 236542 },
    { url = "https://files.pythonhosted.org/packages/5d/32/476a4b5cfaa0ec94d3f808f193301debff2ea42288a099afe60757ef6282/frozenlist-1.7.0-cp312-cp312-musllinux_1_2_armv7l.whl", hash = "sha256:1eaa7e9c6d15df825bf255649e05bd8a74b04a4d2baa1ae46d9c2d00b2ca2cb5", size = 249350 },
    { url = "https://files.pythonhosted.org/packages/8d/ba/9a28042f84a6bf8ea5dbc81cfff8eaef18d78b2a1ad9d51c7bc5b029ad16/frozenlist-1.7.0-cp312-cp312-musllinux_1_2_i686.whl", hash = "sha256:e4389e06714cfa9d47ab87f784a7c5be91d3934cd6e9a7b85beef808297cc025", size = 225093 },
    { url = "https://files.pythonhosted.org/packages/bc/29/3a32959e68f9cf000b04e79ba574527c17e8842e38c91d68214a37455786/frozenlist-1.7.0-cp312-cp312-musllinux_1_2_ppc64le.whl", hash = "sha256:73bd45e1488c40b63fe5a7df892baf9e2a4d4bb6409a2b3b78ac1c6236178e01", size = 245482 },
    { url = "https://files.pythonhosted.org/packages/80/e8/edf2f9e00da553f07f5fa165325cfc302dead715cab6ac8336a5f3d0adc2/frozenlist-1.7.0-cp312-cp312-musllinux_1_2_s390x.whl", hash = "sha256:99886d98e1643269760e5fe0df31e5ae7050788dd288947f7f007209b8c33f08", size = 249590 },
    { url = "https://files.pythonhosted.org/packages/1c/80/9a0eb48b944050f94cc51ee1c413eb14a39543cc4f760ed12657a5a3c45a/frozenlist-1.7.0-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:290a172aae5a4c278c6da8a96222e6337744cd9c77313efe33d5670b9f65fc43", size = 237785 },
    { url = "https://files.pythonhosted.org/packages/f3/74/87601e0fb0369b7a2baf404ea921769c53b7ae00dee7dcfe5162c8c6dbf0/frozenlist-1.7.0-cp312-cp312-win32.whl", hash = "sha256:426c7bc70e07cfebc178bc4c2bf2d861d720c4fff172181eeb4a4c41d4ca2ad3", size = 39487 },
    { url = "https://files.pythonhosted.org/packages/0b/15/c026e9a9fc17585a9d461f65d8593d281fedf55fbf7eb53f16c6df2392f9/frozenlist-1.7.0-cp312-cp312-win_amd64.whl", hash = "sha256:563b72efe5da92e02eb68c59cb37205457c977aa7a449ed1b37e6939e5c47c6a", size = 43874 },
    { url = "https://files.pythonhosted.org/packages/24/90/6b2cebdabdbd50367273c20ff6b57a3dfa89bd0762de02c3a1eb42cb6462/frozenlist-1.7.0-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:ee80eeda5e2a4e660651370ebffd1286542b67e268aa1ac8d6dbe973120ef7ee", size = 79791 },
    { url = "https://files.pythonhosted.org/packages/83/2e/5b70b6a3325363293fe5fc3ae74cdcbc3e996c2a11dde2fd9f1fb0776d19/frozenlist-1.7.0-cp313-cp313-macosx_10_13_x86_64.whl", hash = "sha256:d1a81c85417b914139e3a9b995d4a1c84559afc839a93cf2cb7f15e6e5f6ed2d", size = 47165 },
    { url = "https://files.pythonhosted.org/packages/f4/25/a0895c99270ca6966110f4ad98e87e5662eab416a17e7fd53c364bf8b954/frozenlist-1.7.0-cp313-cp313-macosx_11_0_arm64.whl", hash = "sha256:cbb65198a9132ebc334f237d7b0df163e4de83fb4f2bdfe46c1e654bdb0c5d43", size = 45881 },
    { url = "https://files.pythonhosted.org/packages/19/7c/71bb0bbe0832793c601fff68cd0cf6143753d0c667f9aec93d3c323f4b55/frozenlist-1.7.0-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:dab46c723eeb2c255a64f9dc05b8dd601fde66d6b19cdb82b2e09cc6ff8d8b5d", size = 232409 },
    { url = "https://files.pythonhosted.org/packages/c0/45/ed2798718910fe6eb3ba574082aaceff4528e6323f9a8570be0f7028d8e9/frozenlist-1.7.0-cp313-cp313-manylinux_2_17_armv7l.manylinux2014_armv7l.manylinux_2_31_armv7l.whl", hash = "sha256:6aeac207a759d0dedd2e40745575ae32ab30926ff4fa49b1635def65806fddee", size = 225132 },
    { url = "https://files.pythonhosted.org/packages/ba/e2/8417ae0f8eacb1d071d4950f32f229aa6bf68ab69aab797b72a07ea68d4f/frozenlist-1.7.0-cp313-cp313-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:bd8c4e58ad14b4fa7802b8be49d47993182fdd4023393899632c88fd8cd994eb", size = 237638 },
    { url = "https://files.pythonhosted.org/packages/f8/b7/2ace5450ce85f2af05a871b8c8719b341294775a0a6c5585d5e6170f2ce7/frozenlist-1.7.0-cp313-cp313-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:04fb24d104f425da3540ed83cbfc31388a586a7696142004c577fa61c6298c3f", size = 233539 },
    { url = "https://files.pythonhosted.org/packages/46/b9/6989292c5539553dba63f3c83dc4598186ab2888f67c0dc1d917e6887db6/frozenlist-1.7.0-cp313-cp313-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:6a5c505156368e4ea6b53b5ac23c92d7edc864537ff911d2fb24c140bb175e60", size = 215646 },
    { url = "https://files.pythonhosted.org/packages/72/31/bc8c5c99c7818293458fe745dab4fd5730ff49697ccc82b554eb69f16a24/frozenlist-1.7.0-cp313-cp313-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:8bd7eb96a675f18aa5c553eb7ddc24a43c8c18f22e1f9925528128c052cdbe00", size = 232233 },
    { url = "https://files.pythonhosted.org/packages/59/52/460db4d7ba0811b9ccb85af996019f5d70831f2f5f255f7cc61f86199795/frozenlist-1.7.0-cp313-cp313-musllinux_1_2_aarch64.whl", hash = "sha256:05579bf020096fe05a764f1f84cd104a12f78eaab68842d036772dc6d4870b4b", size = 227996 },
    { url = "https://files.pythonhosted.org/packages/ba/c9/f4b39e904c03927b7ecf891804fd3b4df3db29b9e487c6418e37988d6e9d/frozenlist-1.7.0-cp313-cp313-musllinux_1_2_armv7l.whl", hash = "sha256:376b6222d114e97eeec13d46c486facd41d4f43bab626b7c3f6a8b4e81a5192c", size = 242280 },
    { url = "https://files.pythonhosted.org/packages/b8/33/3f8d6ced42f162d743e3517781566b8481322be321b486d9d262adf70bfb/frozenlist-1.7.0-cp313-cp313-musllinux_1_2_i686.whl", hash = "sha256:0aa7e176ebe115379b5b1c95b4096fb1c17cce0847402e227e712c27bdb5a949", size = 217717 },
    { url = "https://files.pythonhosted.org/packages/3e/e8/ad683e75da6ccef50d0ab0c2b2324b32f84fc88ceee778ed79b8e2d2fe2e/frozenlist-1.7.0-cp313-cp313-musllinux_1_2_ppc64le.whl", hash = "sha256:3fbba20e662b9c2130dc771e332a99eff5da078b2b2648153a40669a6d0e36ca", size = 236644 },
    { url = "https://files.pythonhosted.org/packages/b2/14/8d19ccdd3799310722195a72ac94ddc677541fb4bef4091d8e7775752360/frozenlist-1.7.0-cp313-cp313-musllinux_1_2_s390x.whl", hash = "sha256:f3f4410a0a601d349dd406b5713fec59b4cee7e71678d5b17edda7f4655a940b", size = 238879 },
    { url = "https://files.pythonhosted.org/packages/ce/13/c12bf657494c2fd1079a48b2db49fa4196325909249a52d8f09bc9123fd7/frozenlist-1.7.0-cp313-cp313-musllinux_1_2_x86_64.whl", hash = "sha256:e2cdfaaec6a2f9327bf43c933c0319a7c429058e8537c508964a133dffee412e", size = 232502 },
    { url = "https://files.pythonhosted.org/packages/d7/8b/e7f9dfde869825489382bc0d512c15e96d3964180c9499efcec72e85db7e/frozenlist-1.7.0-cp313-cp313-win32.whl", hash = "sha256:5fc4df05a6591c7768459caba1b342d9ec23fa16195e744939ba5914596ae3e1", size = 39169 },
    { url = "https://files.pythonhosted.org/packages/35/89/a487a98d94205d85745080a37860ff5744b9820a2c9acbcdd9440bfddf98/frozenlist-1.7.0-cp313-cp313-win_amd64.whl", hash = "sha256:52109052b9791a3e6b5d1b65f4b909703984b770694d3eb64fad124c835d7cba", size = 43219 },
    { url = "https://files.pythonhosted.org/packages/56/d5/5c4cf2319a49eddd9dd7145e66c4866bdc6f3dbc67ca3d59685149c11e0d/frozenlist-1.7.0-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:a6f86e4193bb0e235ef6ce3dde5cbabed887e0b11f516ce8a0f4d3b33078ec2d", size = 84345 },
    { url = "https://files.pythonhosted.org/packages/a4/7d/ec2c1e1dc16b85bc9d526009961953df9cec8481b6886debb36ec9107799/frozenlist-1.7.0-cp313-cp313t-macosx_10_13_x86_64.whl", hash = "sha256:82d664628865abeb32d90ae497fb93df398a69bb3434463d172b80fc25b0dd7d", size = 48880 },
    { url = "https://files.pythonhosted.org/packages/69/86/f9596807b03de126e11e7d42ac91e3d0b19a6599c714a1989a4e85eeefc4/frozenlist-1.7.0-cp313-cp313t-macosx_11_0_arm64.whl", hash = "sha256:912a7e8375a1c9a68325a902f3953191b7b292aa3c3fb0d71a216221deca460b", size = 48498 },
    { url = "https://files.pythonhosted.org/packages/5e/cb/df6de220f5036001005f2d726b789b2c0b65f2363b104bbc16f5be8084f8/frozenlist-1.7.0-cp313-cp313t-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:9537c2777167488d539bc5de2ad262efc44388230e5118868e172dd4a552b146", size = 292296 },
    { url = "https://files.pythonhosted.org/packages/83/1f/de84c642f17c8f851a2905cee2dae401e5e0daca9b5ef121e120e19aa825/frozenlist-1.7.0-cp313-cp313t-manylinux_2_17_armv7l.manylinux2014_armv7l.manylinux_2_31_armv7l.whl", hash = "sha256:f34560fb1b4c3e30ba35fa9a13894ba39e5acfc5f60f57d8accde65f46cc5e74", size = 273103 },
    { url = "https://files.pythonhosted.org/packages/88/3c/c840bfa474ba3fa13c772b93070893c6e9d5c0350885760376cbe3b6c1b3/frozenlist-1.7.0-cp313-cp313t-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:acd03d224b0175f5a850edc104ac19040d35419eddad04e7cf2d5986d98427f1", size = 292869 },
    { url = "https://files.pythonhosted.org/packages/a6/1c/3efa6e7d5a39a1d5ef0abeb51c48fb657765794a46cf124e5aca2c7a592c/frozenlist-1.7.0-cp313-cp313t-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:f2038310bc582f3d6a09b3816ab01737d60bf7b1ec70f5356b09e84fb7408ab1", size = 291467 },
    { url = "https://files.pythonhosted.org/packages/4f/00/d5c5e09d4922c395e2f2f6b79b9a20dab4b67daaf78ab92e7729341f61f6/frozenlist-1.7.0-cp313-cp313t-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:b8c05e4c8e5f36e5e088caa1bf78a687528f83c043706640a92cb76cd6999384", size = 266028 },
    { url = "https://files.pythonhosted.org/packages/4e/27/72765be905619dfde25a7f33813ac0341eb6b076abede17a2e3fbfade0cb/frozenlist-1.7.0-cp313-cp313t-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:765bb588c86e47d0b68f23c1bee323d4b703218037765dcf3f25c838c6fecceb", size = 284294 },
    { url = "https://files.pythonhosted.org/packages/88/67/c94103a23001b17808eb7dd1200c156bb69fb68e63fcf0693dde4cd6228c/frozenlist-1.7.0-cp313-cp313t-musllinux_1_2_aarch64.whl", hash = "sha256:32dc2e08c67d86d0969714dd484fd60ff08ff81d1a1e40a77dd34a387e6ebc0c", size = 281898 },
    { url = "https://files.pythonhosted.org/packages/42/34/a3e2c00c00f9e2a9db5653bca3fec306349e71aff14ae45ecc6d0951dd24/frozenlist-1.7.0-cp313-cp313t-musllinux_1_2_armv7l.whl", hash = "sha256:c0303e597eb5a5321b4de9c68e9845ac8f290d2ab3f3e2c864437d3c5a30cd65", size = 290465 },
    { url = "https://files.pythonhosted.org/packages/bb/73/f89b7fbce8b0b0c095d82b008afd0590f71ccb3dee6eee41791cf8cd25fd/frozenlist-1.7.0-cp313-cp313t-musllinux_1_2_i686.whl", hash = "sha256:a47f2abb4e29b3a8d0b530f7c3598badc6b134562b1a5caee867f7c62fee51e3", size = 266385 },
    { url = "https://files.pythonhosted.org/packages/cd/45/e365fdb554159462ca12df54bc59bfa7a9a273ecc21e99e72e597564d1ae/frozenlist-1.7.0-cp313-cp313t-musllinux_1_2_ppc64le.whl", hash = "sha256:3d688126c242a6fabbd92e02633414d40f50bb6002fa4cf995a1d18051525657", size = 288771 },
    { url = "https://files.pythonhosted.org/packages/00/11/47b6117002a0e904f004d70ec5194fe9144f117c33c851e3d51c765962d0/frozenlist-1.7.0-cp313-cp313t-musllinux_1_2_s390x.whl", hash = "sha256:4e7e9652b3d367c7bd449a727dc79d5043f48b88d0cbfd4f9f1060cf2b414104", size = 288206 },
    { url = "https://files.pythonhosted.org/packages/40/37/5f9f3c3fd7f7746082ec67bcdc204db72dad081f4f83a503d33220a92973/frozenlist-1.7.0-cp313-cp313t-musllinux_1_2_x86_64.whl", hash = "sha256:1a85e345b4c43db8b842cab1feb41be5cc0b10a1830e6295b69d7310f99becaf", size = 282620 },
    { url = "https://files.pythonhosted.org/packages/0b/31/8fbc5af2d183bff20f21aa743b4088eac4445d2bb1cdece449ae80e4e2d1/frozenlist-1.7.0-cp313-cp313t-win32.whl", hash = "sha256:3a14027124ddb70dfcee5148979998066897e79f89f64b13328595c4bdf77c81", size = 43059 },
    { url = "https://files.pythonhosted.org/packages/bb/ed/41956f52105b8dbc26e457c5705340c67c8cc2b79f394b79bffc09d0e938/frozenlist-1.7.0-cp313-cp313t-win_amd64.whl", hash = "sha256:3bf8010d71d4507775f658e9823210b7427be36625b387221642725b515dcf3e", size = 47516 },
    { url = "https://files.pythonhosted.org/packages/ee/45/b82e3c16be2182bff01179db177fe144d58b5dc787a7d4492c6ed8b9317f/frozenlist-1.7.0-py3-none-any.whl", hash = "sha256:9a5af342e34f7e97caf8c995864c7a396418ae2859cc6fdf1b1073020d516a7e", size = 13106 },
]

[[package]]
name = "h11"
version = "0.16.0"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/01/ee/02a2c011bdab74c6fb3c75474d40b3052059d95df7e73351460c8588d963/h11-0.16.0.tar.gz", hash = "sha256:4e35b956cf45792e4caa5885e69fba00bdbc6ffafbfa020300e549b208ee5ff1", size = 101250 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/04/4b/29cac41a4d98d144bf5f6d33995617b185d14b22401f75ca86f384e87ff1/h11-0.16.0-py3-none-any.whl", hash = "sha256:63cf8bbe7522de3bf65932fda1d9c2772064ffb3dae62d55932da54b31cb6c86", size = 37515 },
]

[[package]]
name = "httpcore"
version = "1.0.9"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "certifi" },
    { name = "h11" },
]
sdist = { url = "https://files.pythonhosted.org/packages/06/94/82699a10bca87a5556c9c59b5963f2d039dbd239f25bc2a63907a05a14cb/httpcore-1.0.9.tar.gz", hash = "sha256:6e34463af53fd2ab5d807f399a9b45ea31c3dfa2276f15a2c3f00afff6e176e8", size = 85484 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/7e/f5/f66802a942d491edb555dd61e3a9961140fd64c90bce1eafd741609d334d/httpcore-1.0.9-py3-none-any.whl", hash = "sha256:2d400746a40668fc9dec9810239072b40b4484b640a8c38fd654a024c7a1bf55", size = 78784 },
]

[[package]]
name = "httpx"
version = "0.28.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "anyio" },
    { name = "certifi" },
    { name = "httpcore" },
    { name = "idna" },
]
sdist = { url = "https://files.pythonhosted.org/packages/b1/df/48c586a5fe32a0f01324ee087459e112ebb7224f646c0b5023f5e79e9956/httpx-0.28.1.tar.gz", hash = "sha256:75e98c5f16b0f35b567856f597f06ff2270a374470a5c2392242528e3e3e42fc", size = 141406 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/2a/39/e50c7c3a983047577ee07d2a9e53faf5a69493943ec3f6a384bdc792deb2/httpx-0.28.1-py3-none-any.whl", hash = "sha256:d909fcccc110f8c7faf814ca82a9a4d816bc5a6dbfea25d6591d6985b8ba59ad", size = 73517 },
]

[[package]]
name = "idna"
version = "3.10"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/f1/70/7703c29685631f5a7590aa73f1f1d3fa9a380e654b86af429e0934a32f7d/idna-3.10.tar.gz", hash = "sha256:12f65c9b470abda6dc35cf8e63cc574b1c52b11df2c86030af0ac09b01b13ea9", size = 190490 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/76/c6/c88e154df9c4e1a2a66ccf0005a88dfb2650c1dffb6f5ce603dfbd452ce3/idna-3.10-py3-none-any.whl", hash = "sha256:946d195a0d259cbba61165e88e65941f16e9b36ea6ddb97f00452bae8b1287d3", size = 70442 },
]

[[package]]
name = "iniconfig"
version = "2.1.0"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/f2/97/ebf4da567aa6827c909642694d71c9fcf53e5b504f2d96afea02718862f3/iniconfig-2.1.0.tar.gz", hash = "sha256:3abbd2e30b36733fee78f9c7f7308f2d0050e88f0087fd25c2645f63c773e1c7", size = 4793 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/2c/e1/e6716421ea10d38022b952c159d5161ca1193197fb744506875fbb87ea7b/iniconfig-2.1.0-py3-none-any.whl", hash = "sha256:9deba5723312380e77435581c6bf4935c94cbfab9b1ed33ef8d238ea168eb760", size = 6050 },
]

[[package]]
name = "isort"
version = "6.0.1"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/b8/21/1e2a441f74a653a144224d7d21afe8f4169e6c7c20bb13aec3a2dc3815e0/isort-6.0.1.tar.gz", hash = "sha256:1cb5df28dfbc742e490c5e41bad6da41b805b0a8be7bc93cd0fb2a8a890ac450", size = 821955 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/c1/11/114d0a5f4dabbdcedc1125dee0888514c3c3b16d3e9facad87ed96fad97c/isort-6.0.1-py3-none-any.whl", hash = "sha256:2dc5d7f65c9678d94c88dfc29161a320eec67328bc97aad576874cb4be1e9615", size = 94186 },
]

[[package]]
name = "keyboard"
version = "0.13.5"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc", marker = "sys_platform == 'darwin'" },
]
sdist = { url = "https://files.pythonhosted.org/packages/79/75/c969f2258e908c39aadfc57d1cb78247dc49e6d36371bb3a48c194640c01/keyboard-0.13.5.zip", hash = "sha256:63ed83305955939ca5c9a73755e5cc43e8242263f5ad5fd3bb7e0b032f3d308b", size = 71798 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/55/88/287159903c5b3fc6d47b651c7ab65a54dcf9c9916de546188a7f62870d6d/keyboard-0.13.5-py3-none-any.whl", hash = "sha256:8e9c2422f1217e0bd84489b9ecd361027cc78415828f4fe4f88dd4acd587947b", size = 58098 },
]

[[package]]
name = "markdown-it-py"
version = "3.0.0"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "mdurl" },
]
sdist = { url = "https://files.pythonhosted.org/packages/38/71/3b932df36c1a044d397a1f92d1cf91ee0a503d91e470cbd670aa66b07ed0/markdown-it-py-3.0.0.tar.gz", hash = "sha256:e3f60a94fa066dc52ec76661e37c851cb232d92f9886b15cb560aaada2df8feb", size = 74596 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/42/d7/1ec15b46af6af88f19b8e5ffea08fa375d433c998b8a7639e76935c14f1f/markdown_it_py-3.0.0-py3-none-any.whl", hash = "sha256:355216845c60bd96232cd8d8c40e8f9765cc86f46880e43a8fd22dc1a1a8cab1", size = 87528 },
]

[[package]]
name = "mdurl"
version = "0.1.2"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/d6/54/cfe61301667036ec958cb99bd3efefba235e65cdeb9c84d24a8293ba1d90/mdurl-0.1.2.tar.gz", hash = "sha256:bb413d29f5eea38f31dd4754dd7377d4465116fb207585f97bf925588687c1ba", size = 8729 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/b3/38/89ba8ad64ae25be8de66a6d463314cf1eb366222074cfda9ee839c56a4b4/mdurl-0.1.2-py3-none-any.whl", hash = "sha256:84008a41e51615a49fc9966191ff91509e3c40b939176e643fd50a5c2196b8f8", size = 9979 },
]

[[package]]
name = "multidict"
version = "6.6.3"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/3d/2c/5dad12e82fbdf7470f29bff2171484bf07cb3b16ada60a6589af8f376440/multidict-6.6.3.tar.gz", hash = "sha256:798a9eb12dab0a6c2e29c1de6f3468af5cb2da6053a20dfa3344907eed0937cc", size = 101006 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/0e/a0/6b57988ea102da0623ea814160ed78d45a2645e4bbb499c2896d12833a70/multidict-6.6.3-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:056bebbeda16b2e38642d75e9e5310c484b7c24e3841dc0fb943206a72ec89d6", size = 76514 },
    { url = "https://files.pythonhosted.org/packages/07/7a/d1e92665b0850c6c0508f101f9cf0410c1afa24973e1115fe9c6a185ebf7/multidict-6.6.3-cp312-cp312-macosx_10_13_x86_64.whl", hash = "sha256:e5f481cccb3c5c5e5de5d00b5141dc589c1047e60d07e85bbd7dea3d4580d63f", size = 45394 },
    { url = "https://files.pythonhosted.org/packages/52/6f/dd104490e01be6ef8bf9573705d8572f8c2d2c561f06e3826b081d9e6591/multidict-6.6.3-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:10bea2ee839a759ee368b5a6e47787f399b41e70cf0c20d90dfaf4158dfb4e55", size = 43590 },
    { url = "https://files.pythonhosted.org/packages/44/fe/06e0e01b1b0611e6581b7fd5a85b43dacc08b6cea3034f902f383b0873e5/multidict-6.6.3-cp312-cp312-manylinux1_i686.manylinux2014_i686.manylinux_2_17_i686.manylinux_2_5_i686.whl", hash = "sha256:2334cfb0fa9549d6ce2c21af2bfbcd3ac4ec3646b1b1581c88e3e2b1779ec92b", size = 237292 },
    { url = "https://files.pythonhosted.org/packages/ce/71/4f0e558fb77696b89c233c1ee2d92f3e1d5459070a0e89153c9e9e804186/multidict-6.6.3-cp312-cp312-manylinux2014_aarch64.manylinux_2_17_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:b8fee016722550a2276ca2cb5bb624480e0ed2bd49125b2b73b7010b9090e888", size = 258385 },
    { url = "https://files.pythonhosted.org/packages/e3/25/cca0e68228addad24903801ed1ab42e21307a1b4b6dd2cf63da5d3ae082a/multidict-6.6.3-cp312-cp312-manylinux2014_armv7l.manylinux_2_17_armv7l.manylinux_2_31_armv7l.whl", hash = "sha256:e5511cb35f5c50a2db21047c875eb42f308c5583edf96bd8ebf7d770a9d68f6d", size = 242328 },
    { url = "https://files.pythonhosted.org/packages/6e/a3/46f2d420d86bbcb8fe660b26a10a219871a0fbf4d43cb846a4031533f3e0/multidict-6.6.3-cp312-cp312-manylinux2014_ppc64le.manylinux_2_17_ppc64le.manylinux_2_28_ppc64le.whl", hash = "sha256:712b348f7f449948e0a6c4564a21c7db965af900973a67db432d724619b3c680", size = 268057 },
    { url = "https://files.pythonhosted.org/packages/9e/73/1c743542fe00794a2ec7466abd3f312ccb8fad8dff9f36d42e18fb1ec33e/multidict-6.6.3-cp312-cp312-manylinux2014_s390x.manylinux_2_17_s390x.manylinux_2_28_s390x.whl", hash = "sha256:e4e15d2138ee2694e038e33b7c3da70e6b0ad8868b9f8094a72e1414aeda9c1a", size = 269341 },
    { url = "https://files.pythonhosted.org/packages/a4/11/6ec9dcbe2264b92778eeb85407d1df18812248bf3506a5a1754bc035db0c/multidict-6.6.3-cp312-cp312-manylinux2014_x86_64.manylinux_2_17_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:8df25594989aebff8a130f7899fa03cbfcc5d2b5f4a461cf2518236fe6f15961", size = 256081 },
    { url = "https://files.pythonhosted.org/packages/9b/2b/631b1e2afeb5f1696846d747d36cda075bfdc0bc7245d6ba5c319278d6c4/multidict-6.6.3-cp312-cp312-musllinux_1_2_aarch64.whl", hash = "sha256:159ca68bfd284a8860f8d8112cf0521113bffd9c17568579e4d13d1f1dc76b65", size = 253581 },
    { url = "https://files.pythonhosted.org/packages/bf/0e/7e3b93f79efeb6111d3bf9a1a69e555ba1d07ad1c11bceb56b7310d0d7ee/multidict-6.6.3-cp312-cp312-musllinux_1_2_armv7l.whl", hash = "sha256:e098c17856a8c9ade81b4810888c5ad1914099657226283cab3062c0540b0643", size = 250750 },
    { url = "https://files.pythonhosted.org/packages/ad/9e/086846c1d6601948e7de556ee464a2d4c85e33883e749f46b9547d7b0704/multidict-6.6.3-cp312-cp312-musllinux_1_2_i686.whl", hash = "sha256:67c92ed673049dec52d7ed39f8cf9ebbadf5032c774058b4406d18c8f8fe7063", size = 251548 },
    { url = "https://files.pythonhosted.org/packages/8c/7b/86ec260118e522f1a31550e87b23542294880c97cfbf6fb18cc67b044c66/multidict-6.6.3-cp312-cp312-musllinux_1_2_ppc64le.whl", hash = "sha256:bd0578596e3a835ef451784053cfd327d607fc39ea1a14812139339a18a0dbc3", size = 262718 },
    { url = "https://files.pythonhosted.org/packages/8c/bd/22ce8f47abb0be04692c9fc4638508b8340987b18691aa7775d927b73f72/multidict-6.6.3-cp312-cp312-musllinux_1_2_s390x.whl", hash = "sha256:346055630a2df2115cd23ae271910b4cae40f4e336773550dca4889b12916e75", size = 259603 },
    { url = "https://files.pythonhosted.org/packages/07/9c/91b7ac1691be95cd1f4a26e36a74b97cda6aa9820632d31aab4410f46ebd/multidict-6.6.3-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:555ff55a359302b79de97e0468e9ee80637b0de1fce77721639f7cd9440b3a10", size = 251351 },
    { url = "https://files.pythonhosted.org/packages/6f/5c/4d7adc739884f7a9fbe00d1eac8c034023ef8bad71f2ebe12823ca2e3649/multidict-6.6.3-cp312-cp312-win32.whl", hash = "sha256:73ab034fb8d58ff85c2bcbadc470efc3fafeea8affcf8722855fb94557f14cc5", size = 41860 },
    { url = "https://files.pythonhosted.org/packages/6a/a3/0fbc7afdf7cb1aa12a086b02959307848eb6bcc8f66fcb66c0cb57e2a2c1/multidict-6.6.3-cp312-cp312-win_amd64.whl", hash = "sha256:04cbcce84f63b9af41bad04a54d4cc4e60e90c35b9e6ccb130be2d75b71f8c17", size = 45982 },
    { url = "https://files.pythonhosted.org/packages/b8/95/8c825bd70ff9b02462dc18d1295dd08d3e9e4eb66856d292ffa62cfe1920/multidict-6.6.3-cp312-cp312-win_arm64.whl", hash = "sha256:0f1130b896ecb52d2a1e615260f3ea2af55fa7dc3d7c3003ba0c3121a759b18b", size = 43210 },
    { url = "https://files.pythonhosted.org/packages/52/1d/0bebcbbb4f000751fbd09957257903d6e002943fc668d841a4cf2fb7f872/multidict-6.6.3-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:540d3c06d48507357a7d57721e5094b4f7093399a0106c211f33540fdc374d55", size = 75843 },
    { url = "https://files.pythonhosted.org/packages/07/8f/cbe241b0434cfe257f65c2b1bcf9e8d5fb52bc708c5061fb29b0fed22bdf/multidict-6.6.3-cp313-cp313-macosx_10_13_x86_64.whl", hash = "sha256:9c19cea2a690f04247d43f366d03e4eb110a0dc4cd1bbeee4d445435428ed35b", size = 45053 },
    { url = "https://files.pythonhosted.org/packages/32/d2/0b3b23f9dbad5b270b22a3ac3ea73ed0a50ef2d9a390447061178ed6bdb8/multidict-6.6.3-cp313-cp313-macosx_11_0_arm64.whl", hash = "sha256:7af039820cfd00effec86bda5d8debef711a3e86a1d3772e85bea0f243a4bd65", size = 43273 },
    { url = "https://files.pythonhosted.org/packages/fd/fe/6eb68927e823999e3683bc49678eb20374ba9615097d085298fd5b386564/multidict-6.6.3-cp313-cp313-manylinux1_i686.manylinux2014_i686.manylinux_2_17_i686.manylinux_2_5_i686.whl", hash = "sha256:500b84f51654fdc3944e936f2922114349bf8fdcac77c3092b03449f0e5bc2b3", size = 237124 },
    { url = "https://files.pythonhosted.org/packages/e7/ab/320d8507e7726c460cb77117848b3834ea0d59e769f36fdae495f7669929/multidict-6.6.3-cp313-cp313-manylinux2014_aarch64.manylinux_2_17_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:f3fc723ab8a5c5ed6c50418e9bfcd8e6dceba6c271cee6728a10a4ed8561520c", size = 256892 },
    { url = "https://files.pythonhosted.org/packages/76/60/38ee422db515ac69834e60142a1a69111ac96026e76e8e9aa347fd2e4591/multidict-6.6.3-cp313-cp313-manylinux2014_armv7l.manylinux_2_17_armv7l.manylinux_2_31_armv7l.whl", hash = "sha256:94c47ea3ade005b5976789baaed66d4de4480d0a0bf31cef6edaa41c1e7b56a6", size = 240547 },
    { url = "https://files.pythonhosted.org/packages/27/fb/905224fde2dff042b030c27ad95a7ae744325cf54b890b443d30a789b80e/multidict-6.6.3-cp313-cp313-manylinux2014_ppc64le.manylinux_2_17_ppc64le.manylinux_2_28_ppc64le.whl", hash = "sha256:dbc7cf464cc6d67e83e136c9f55726da3a30176f020a36ead246eceed87f1cd8", size = 266223 },
    { url = "https://files.pythonhosted.org/packages/76/35/dc38ab361051beae08d1a53965e3e1a418752fc5be4d3fb983c5582d8784/multidict-6.6.3-cp313-cp313-manylinux2014_s390x.manylinux_2_17_s390x.manylinux_2_28_s390x.whl", hash = "sha256:900eb9f9da25ada070f8ee4a23f884e0ee66fe4e1a38c3af644256a508ad81ca", size = 267262 },
    { url = "https://files.pythonhosted.org/packages/1f/a3/0a485b7f36e422421b17e2bbb5a81c1af10eac1d4476f2ff92927c730479/multidict-6.6.3-cp313-cp313-manylinux2014_x86_64.manylinux_2_17_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:7c6df517cf177da5d47ab15407143a89cd1a23f8b335f3a28d57e8b0a3dbb884", size = 254345 },
    { url = "https://files.pythonhosted.org/packages/b4/59/bcdd52c1dab7c0e0d75ff19cac751fbd5f850d1fc39172ce809a74aa9ea4/multidict-6.6.3-cp313-cp313-musllinux_1_2_aarch64.whl", hash = "sha256:4ef421045f13879e21c994b36e728d8e7d126c91a64b9185810ab51d474f27e7", size = 252248 },
    { url = "https://files.pythonhosted.org/packages/bb/a4/2d96aaa6eae8067ce108d4acee6f45ced5728beda55c0f02ae1072c730d1/multidict-6.6.3-cp313-cp313-musllinux_1_2_armv7l.whl", hash = "sha256:6c1e61bb4f80895c081790b6b09fa49e13566df8fbff817da3f85b3a8192e36b", size = 250115 },
    { url = "https://files.pythonhosted.org/packages/25/d2/ed9f847fa5c7d0677d4f02ea2c163d5e48573de3f57bacf5670e43a5ffaa/multidict-6.6.3-cp313-cp313-musllinux_1_2_i686.whl", hash = "sha256:e5e8523bb12d7623cd8300dbd91b9e439a46a028cd078ca695eb66ba31adee3c", size = 249649 },
    { url = "https://files.pythonhosted.org/packages/1f/af/9155850372563fc550803d3f25373308aa70f59b52cff25854086ecb4a79/multidict-6.6.3-cp313-cp313-musllinux_1_2_ppc64le.whl", hash = "sha256:ef58340cc896219e4e653dade08fea5c55c6df41bcc68122e3be3e9d873d9a7b", size = 261203 },
    { url = "https://files.pythonhosted.org/packages/36/2f/c6a728f699896252cf309769089568a33c6439626648843f78743660709d/multidict-6.6.3-cp313-cp313-musllinux_1_2_s390x.whl", hash = "sha256:fc9dc435ec8699e7b602b94fe0cd4703e69273a01cbc34409af29e7820f777f1", size = 258051 },
    { url = "https://files.pythonhosted.org/packages/d0/60/689880776d6b18fa2b70f6cc74ff87dd6c6b9b47bd9cf74c16fecfaa6ad9/multidict-6.6.3-cp313-cp313-musllinux_1_2_x86_64.whl", hash = "sha256:9e864486ef4ab07db5e9cb997bad2b681514158d6954dd1958dfb163b83d53e6", size = 249601 },
    { url = "https://files.pythonhosted.org/packages/75/5e/325b11f2222a549019cf2ef879c1f81f94a0d40ace3ef55cf529915ba6cc/multidict-6.6.3-cp313-cp313-win32.whl", hash = "sha256:5633a82fba8e841bc5c5c06b16e21529573cd654f67fd833650a215520a6210e", size = 41683 },
    { url = "https://files.pythonhosted.org/packages/b1/ad/cf46e73f5d6e3c775cabd2a05976547f3f18b39bee06260369a42501f053/multidict-6.6.3-cp313-cp313-win_amd64.whl", hash = "sha256:e93089c1570a4ad54c3714a12c2cef549dc9d58e97bcded193d928649cab78e9", size = 45811 },
    { url = "https://files.pythonhosted.org/packages/c5/c9/2e3fe950db28fb7c62e1a5f46e1e38759b072e2089209bc033c2798bb5ec/multidict-6.6.3-cp313-cp313-win_arm64.whl", hash = "sha256:c60b401f192e79caec61f166da9c924e9f8bc65548d4246842df91651e83d600", size = 43056 },
    { url = "https://files.pythonhosted.org/packages/3a/58/aaf8114cf34966e084a8cc9517771288adb53465188843d5a19862cb6dc3/multidict-6.6.3-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:02fd8f32d403a6ff13864b0851f1f523d4c988051eea0471d4f1fd8010f11134", size = 82811 },
    { url = "https://files.pythonhosted.org/packages/71/af/5402e7b58a1f5b987a07ad98f2501fdba2a4f4b4c30cf114e3ce8db64c87/multidict-6.6.3-cp313-cp313t-macosx_10_13_x86_64.whl", hash = "sha256:f3aa090106b1543f3f87b2041eef3c156c8da2aed90c63a2fbed62d875c49c37", size = 48304 },
    { url = "https://files.pythonhosted.org/packages/39/65/ab3c8cafe21adb45b24a50266fd747147dec7847425bc2a0f6934b3ae9ce/multidict-6.6.3-cp313-cp313t-macosx_11_0_arm64.whl", hash = "sha256:e924fb978615a5e33ff644cc42e6aa241effcf4f3322c09d4f8cebde95aff5f8", size = 46775 },
    { url = "https://files.pythonhosted.org/packages/49/ba/9fcc1b332f67cc0c0c8079e263bfab6660f87fe4e28a35921771ff3eea0d/multidict-6.6.3-cp313-cp313t-manylinux1_i686.manylinux2014_i686.manylinux_2_17_i686.manylinux_2_5_i686.whl", hash = "sha256:b9fe5a0e57c6dbd0e2ce81ca66272282c32cd11d31658ee9553849d91289e1c1", size = 229773 },
    { url = "https://files.pythonhosted.org/packages/a4/14/0145a251f555f7c754ce2dcbcd012939bbd1f34f066fa5d28a50e722a054/multidict-6.6.3-cp313-cp313t-manylinux2014_aarch64.manylinux_2_17_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:b24576f208793ebae00280c59927c3b7c2a3b1655e443a25f753c4611bc1c373", size = 250083 },
    { url = "https://files.pythonhosted.org/packages/9e/d4/d5c0bd2bbb173b586c249a151a26d2fb3ec7d53c96e42091c9fef4e1f10c/multidict-6.6.3-cp313-cp313t-manylinux2014_armv7l.manylinux_2_17_armv7l.manylinux_2_31_armv7l.whl", hash = "sha256:135631cb6c58eac37d7ac0df380294fecdc026b28837fa07c02e459c7fb9c54e", size = 228980 },
    { url = "https://files.pythonhosted.org/packages/21/32/c9a2d8444a50ec48c4733ccc67254100c10e1c8ae8e40c7a2d2183b59b97/multidict-6.6.3-cp313-cp313t-manylinux2014_ppc64le.manylinux_2_17_ppc64le.manylinux_2_28_ppc64le.whl", hash = "sha256:274d416b0df887aef98f19f21578653982cfb8a05b4e187d4a17103322eeaf8f", size = 257776 },
    { url = "https://files.pythonhosted.org/packages/68/d0/14fa1699f4ef629eae08ad6201c6b476098f5efb051b296f4c26be7a9fdf/multidict-6.6.3-cp313-cp313t-manylinux2014_s390x.manylinux_2_17_s390x.manylinux_2_28_s390x.whl", hash = "sha256:e252017a817fad7ce05cafbe5711ed40faeb580e63b16755a3a24e66fa1d87c0", size = 256882 },
    { url = "https://files.pythonhosted.org/packages/da/88/84a27570fbe303c65607d517a5f147cd2fc046c2d1da02b84b17b9bdc2aa/multidict-6.6.3-cp313-cp313t-manylinux2014_x86_64.manylinux_2_17_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:2e4cc8d848cd4fe1cdee28c13ea79ab0ed37fc2e89dd77bac86a2e7959a8c3bc", size = 247816 },
    { url = "https://files.pythonhosted.org/packages/1c/60/dca352a0c999ce96a5d8b8ee0b2b9f729dcad2e0b0c195f8286269a2074c/multidict-6.6.3-cp313-cp313t-musllinux_1_2_aarch64.whl", hash = "sha256:9e236a7094b9c4c1b7585f6b9cca34b9d833cf079f7e4c49e6a4a6ec9bfdc68f", size = 245341 },
    { url = "https://files.pythonhosted.org/packages/50/ef/433fa3ed06028f03946f3993223dada70fb700f763f70c00079533c34578/multidict-6.6.3-cp313-cp313t-musllinux_1_2_armv7l.whl", hash = "sha256:e0cb0ab69915c55627c933f0b555a943d98ba71b4d1c57bc0d0a66e2567c7471", size = 235854 },
    { url = "https://files.pythonhosted.org/packages/1b/1f/487612ab56fbe35715320905215a57fede20de7db40a261759690dc80471/multidict-6.6.3-cp313-cp313t-musllinux_1_2_i686.whl", hash = "sha256:81ef2f64593aba09c5212a3d0f8c906a0d38d710a011f2f42759704d4557d3f2", size = 243432 },
    { url = "https://files.pythonhosted.org/packages/da/6f/ce8b79de16cd885c6f9052c96a3671373d00c59b3ee635ea93e6e81b8ccf/multidict-6.6.3-cp313-cp313t-musllinux_1_2_ppc64le.whl", hash = "sha256:b9cbc60010de3562545fa198bfc6d3825df430ea96d2cc509c39bd71e2e7d648", size = 252731 },
    { url = "https://files.pythonhosted.org/packages/bb/fe/a2514a6aba78e5abefa1624ca85ae18f542d95ac5cde2e3815a9fbf369aa/multidict-6.6.3-cp313-cp313t-musllinux_1_2_s390x.whl", hash = "sha256:70d974eaaa37211390cd02ef93b7e938de564bbffa866f0b08d07e5e65da783d", size = 247086 },
    { url = "https://files.pythonhosted.org/packages/8c/22/b788718d63bb3cce752d107a57c85fcd1a212c6c778628567c9713f9345a/multidict-6.6.3-cp313-cp313t-musllinux_1_2_x86_64.whl", hash = "sha256:3713303e4a6663c6d01d648a68f2848701001f3390a030edaaf3fc949c90bf7c", size = 243338 },
    { url = "https://files.pythonhosted.org/packages/22/d6/fdb3d0670819f2228f3f7d9af613d5e652c15d170c83e5f1c94fbc55a25b/multidict-6.6.3-cp313-cp313t-win32.whl", hash = "sha256:639ecc9fe7cd73f2495f62c213e964843826f44505a3e5d82805aa85cac6f89e", size = 47812 },
    { url = "https://files.pythonhosted.org/packages/b6/d6/a9d2c808f2c489ad199723197419207ecbfbc1776f6e155e1ecea9c883aa/multidict-6.6.3-cp313-cp313t-win_amd64.whl", hash = "sha256:9f97e181f344a0ef3881b573d31de8542cc0dbc559ec68c8f8b5ce2c2e91646d", size = 53011 },
    { url = "https://files.pythonhosted.org/packages/f2/40/b68001cba8188dd267590a111f9661b6256debc327137667e832bf5d66e8/multidict-6.6.3-cp313-cp313t-win_arm64.whl", hash = "sha256:ce8b7693da41a3c4fde5871c738a81490cea5496c671d74374c8ab889e1834fb", size = 45254 },
    { url = "https://files.pythonhosted.org/packages/d8/30/9aec301e9772b098c1f5c0ca0279237c9766d94b97802e9888010c64b0ed/multidict-6.6.3-py3-none-any.whl", hash = "sha256:8db10f29c7541fc5da4defd8cd697e1ca429db743fa716325f236079b96f775a", size = 12313 },
]

[[package]]
name = "mypy"
version = "1.17.0"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "mypy-extensions" },
    { name = "pathspec" },
    { name = "typing-extensions" },
]
sdist = { url = "https://files.pythonhosted.org/packages/1e/e3/034322d5a779685218ed69286c32faa505247f1f096251ef66c8fd203b08/mypy-1.17.0.tar.gz", hash = "sha256:e5d7ccc08ba089c06e2f5629c660388ef1fee708444f1dee0b9203fa031dee03", size = 3352114 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/12/e9/e6824ed620bbf51d3bf4d6cbbe4953e83eaf31a448d1b3cfb3620ccb641c/mypy-1.17.0-cp312-cp312-macosx_10_13_x86_64.whl", hash = "sha256:f986f1cab8dbec39ba6e0eaa42d4d3ac6686516a5d3dccd64be095db05ebc6bb", size = 11086395 },
    { url = "https://files.pythonhosted.org/packages/ba/51/a4afd1ae279707953be175d303f04a5a7bd7e28dc62463ad29c1c857927e/mypy-1.17.0-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:51e455a54d199dd6e931cd7ea987d061c2afbaf0960f7f66deef47c90d1b304d", size = 10120052 },
    { url = "https://files.pythonhosted.org/packages/8a/71/19adfeac926ba8205f1d1466d0d360d07b46486bf64360c54cb5a2bd86a8/mypy-1.17.0-cp312-cp312-manylinux2014_aarch64.manylinux_2_17_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:3204d773bab5ff4ebbd1f8efa11b498027cd57017c003ae970f310e5b96be8d8", size = 11861806 },
    { url = "https://files.pythonhosted.org/packages/0b/64/d6120eca3835baf7179e6797a0b61d6c47e0bc2324b1f6819d8428d5b9ba/mypy-1.17.0-cp312-cp312-manylinux2014_x86_64.manylinux_2_17_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:1051df7ec0886fa246a530ae917c473491e9a0ba6938cfd0ec2abc1076495c3e", size = 12744371 },
    { url = "https://files.pythonhosted.org/packages/1f/dc/56f53b5255a166f5bd0f137eed960e5065f2744509dfe69474ff0ba772a5/mypy-1.17.0-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:f773c6d14dcc108a5b141b4456b0871df638eb411a89cd1c0c001fc4a9d08fc8", size = 12914558 },
    { url = "https://files.pythonhosted.org/packages/69/ac/070bad311171badc9add2910e7f89271695a25c136de24bbafc7eded56d5/mypy-1.17.0-cp312-cp312-win_amd64.whl", hash = "sha256:1619a485fd0e9c959b943c7b519ed26b712de3002d7de43154a489a2d0fd817d", size = 9585447 },
    { url = "https://files.pythonhosted.org/packages/be/7b/5f8ab461369b9e62157072156935cec9d272196556bdc7c2ff5f4c7c0f9b/mypy-1.17.0-cp313-cp313-macosx_10_13_x86_64.whl", hash = "sha256:2c41aa59211e49d717d92b3bb1238c06d387c9325d3122085113c79118bebb06", size = 11070019 },
    { url = "https://files.pythonhosted.org/packages/9c/f8/c49c9e5a2ac0badcc54beb24e774d2499748302c9568f7f09e8730e953fa/mypy-1.17.0-cp313-cp313-macosx_11_0_arm64.whl", hash = "sha256:0e69db1fb65b3114f98c753e3930a00514f5b68794ba80590eb02090d54a5d4a", size = 10114457 },
    { url = "https://files.pythonhosted.org/packages/89/0c/fb3f9c939ad9beed3e328008b3fb90b20fda2cddc0f7e4c20dbefefc3b33/mypy-1.17.0-cp313-cp313-manylinux2014_aarch64.manylinux_2_17_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:03ba330b76710f83d6ac500053f7727270b6b8553b0423348ffb3af6f2f7b889", size = 11857838 },
    { url = "https://files.pythonhosted.org/packages/4c/66/85607ab5137d65e4f54d9797b77d5a038ef34f714929cf8ad30b03f628df/mypy-1.17.0-cp313-cp313-manylinux2014_x86_64.manylinux_2_17_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:037bc0f0b124ce46bfde955c647f3e395c6174476a968c0f22c95a8d2f589bba", size = 12731358 },
    { url = "https://files.pythonhosted.org/packages/73/d0/341dbbfb35ce53d01f8f2969facbb66486cee9804048bf6c01b048127501/mypy-1.17.0-cp313-cp313-musllinux_1_2_x86_64.whl", hash = "sha256:c38876106cb6132259683632b287238858bd58de267d80defb6f418e9ee50658", size = 12917480 },
    { url = "https://files.pythonhosted.org/packages/64/63/70c8b7dbfc520089ac48d01367a97e8acd734f65bd07813081f508a8c94c/mypy-1.17.0-cp313-cp313-win_amd64.whl", hash = "sha256:d30ba01c0f151998f367506fab31c2ac4527e6a7b2690107c7a7f9e3cb419a9c", size = 9589666 },
    { url = "https://files.pythonhosted.org/packages/e3/fc/ee058cc4316f219078464555873e99d170bde1d9569abd833300dbeb484a/mypy-1.17.0-py3-none-any.whl", hash = "sha256:15d9d0018237ab058e5de3d8fce61b6fa72cc59cc78fd91f1b474bce12abf496", size = 2283195 },
]

[[package]]
name = "mypy-extensions"
version = "1.1.0"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/a2/6e/371856a3fb9d31ca8dac321cda606860fa4548858c0cc45d9d1d4ca2628b/mypy_extensions-1.1.0.tar.gz", hash = "sha256:52e68efc3284861e772bbcd66823fde5ae21fd2fdb51c62a211403730b916558", size = 6343 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/79/7b/2c79738432f5c924bef5071f933bcc9efd0473bac3b4aa584a6f7c1c8df8/mypy_extensions-1.1.0-py3-none-any.whl", hash = "sha256:1be4cccdb0f2482337c4743e60421de3a356cd97508abadd57d47403e94f5505", size = 4963 },
]

[[package]]
name = "packaging"
version = "25.0"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/a1/d4/1fc4078c65507b51b96ca8f8c3ba19e6a61c8253c72794544580a7b6c24d/packaging-25.0.tar.gz", hash = "sha256:d443872c98d677bf60f6a1f2f8c1cb748e8fe762d2bf9d3148b5599295b0fc4f", size = 165727 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/20/12/38679034af332785aac8774540895e234f4d07f7545804097de4b666afd8/packaging-25.0-py3-none-any.whl", hash = "sha256:29572ef2b1f17581046b3a2227d5c611fb25ec70ca1ba8554b24b0e69331a484", size = 66469 },
]

[[package]]
name = "pathspec"
version = "0.12.1"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/ca/bc/f35b8446f4531a7cb215605d100cd88b7ac6f44ab3fc94870c120ab3adbf/pathspec-0.12.1.tar.gz", hash = "sha256:a482d51503a1ab33b1c67a6c3813a26953dbdc71c31dacaef9a838c4e29f5712", size = 51043 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/cc/20/ff623b09d963f88bfde16306a54e12ee5ea43e9b597108672ff3a408aad6/pathspec-0.12.1-py3-none-any.whl", hash = "sha256:a0d503e138a4c123b27490a4f7beda6a01c6f288df0e4a8b79c7eb0dc7b4cc08", size = 31191 },
]

[[package]]
name = "platformdirs"
version = "4.3.8"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/fe/8b/3c73abc9c759ecd3f1f7ceff6685840859e8070c4d947c93fae71f6a0bf2/platformdirs-4.3.8.tar.gz", hash = "sha256:3d512d96e16bcb959a814c9f348431070822a6496326a4be0911c40b5a74c2bc", size = 21362 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/fe/39/979e8e21520d4e47a0bbe349e2713c0aac6f3d853d0e5b34d76206c439aa/platformdirs-4.3.8-py3-none-any.whl", hash = "sha256:ff7059bb7eb1179e2685604f4aaf157cfd9535242bd23742eadc3c13542139b4", size = 18567 },
]

[[package]]
name = "pluggy"
version = "1.6.0"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/f9/e2/3e91f31a7d2b083fe6ef3fa267035b518369d9511ffab804f839851d2779/pluggy-1.6.0.tar.gz", hash = "sha256:7dcc130b76258d33b90f61b658791dede3486c3e6bfb003ee5c9bfb396dd22f3", size = 69412 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/54/20/4d324d65cc6d9205fabedc306948156824eb9f0ee1633355a8f7ec5c66bf/pluggy-1.6.0-py3-none-any.whl", hash = "sha256:e920276dd6813095e9377c0bc5566d94c932c33b27a3e3945d8389c374dd4746", size = 20538 },
]

[[package]]
name = "propcache"
version = "0.3.2"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/a6/16/43264e4a779dd8588c21a70f0709665ee8f611211bdd2c87d952cfa7c776/propcache-0.3.2.tar.gz", hash = "sha256:20d7d62e4e7ef05f221e0db2856b979540686342e7dd9973b815599c7057e168", size = 44139 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/a8/42/9ca01b0a6f48e81615dca4765a8f1dd2c057e0540f6116a27dc5ee01dfb6/propcache-0.3.2-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:8de106b6c84506b31c27168582cd3cb3000a6412c16df14a8628e5871ff83c10", size = 73674 },
    { url = "https://files.pythonhosted.org/packages/af/6e/21293133beb550f9c901bbece755d582bfaf2176bee4774000bd4dd41884/propcache-0.3.2-cp312-cp312-macosx_10_13_x86_64.whl", hash = "sha256:28710b0d3975117239c76600ea351934ac7b5ff56e60953474342608dbbb6154", size = 43570 },
    { url = "https://files.pythonhosted.org/packages/0c/c8/0393a0a3a2b8760eb3bde3c147f62b20044f0ddac81e9d6ed7318ec0d852/propcache-0.3.2-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:ce26862344bdf836650ed2487c3d724b00fbfec4233a1013f597b78c1cb73615", size = 43094 },
    { url = "https://files.pythonhosted.org/packages/37/2c/489afe311a690399d04a3e03b069225670c1d489eb7b044a566511c1c498/propcache-0.3.2-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:bca54bd347a253af2cf4544bbec232ab982f4868de0dd684246b67a51bc6b1db", size = 226958 },
    { url = "https://files.pythonhosted.org/packages/9d/ca/63b520d2f3d418c968bf596839ae26cf7f87bead026b6192d4da6a08c467/propcache-0.3.2-cp312-cp312-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:55780d5e9a2ddc59711d727226bb1ba83a22dd32f64ee15594b9392b1f544eb1", size = 234894 },
    { url = "https://files.pythonhosted.org/packages/11/60/1d0ed6fff455a028d678df30cc28dcee7af77fa2b0e6962ce1df95c9a2a9/propcache-0.3.2-cp312-cp312-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:035e631be25d6975ed87ab23153db6a73426a48db688070d925aa27e996fe93c", size = 233672 },
    { url = "https://files.pythonhosted.org/packages/37/7c/54fd5301ef38505ab235d98827207176a5c9b2aa61939b10a460ca53e123/propcache-0.3.2-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:ee6f22b6eaa39297c751d0e80c0d3a454f112f5c6481214fcf4c092074cecd67", size = 224395 },
    { url = "https://files.pythonhosted.org/packages/ee/1a/89a40e0846f5de05fdc6779883bf46ba980e6df4d2ff8fb02643de126592/propcache-0.3.2-cp312-cp312-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:7ca3aee1aa955438c4dba34fc20a9f390e4c79967257d830f137bd5a8a32ed3b", size = 212510 },
    { url = "https://files.pythonhosted.org/packages/5e/33/ca98368586c9566a6b8d5ef66e30484f8da84c0aac3f2d9aec6d31a11bd5/propcache-0.3.2-cp312-cp312-musllinux_1_2_aarch64.whl", hash = "sha256:7a4f30862869fa2b68380d677cc1c5fcf1e0f2b9ea0cf665812895c75d0ca3b8", size = 222949 },
    { url = "https://files.pythonhosted.org/packages/ba/11/ace870d0aafe443b33b2f0b7efdb872b7c3abd505bfb4890716ad7865e9d/propcache-0.3.2-cp312-cp312-musllinux_1_2_armv7l.whl", hash = "sha256:b77ec3c257d7816d9f3700013639db7491a434644c906a2578a11daf13176251", size = 217258 },
    { url = "https://files.pythonhosted.org/packages/5b/d2/86fd6f7adffcfc74b42c10a6b7db721d1d9ca1055c45d39a1a8f2a740a21/propcache-0.3.2-cp312-cp312-musllinux_1_2_i686.whl", hash = "sha256:cab90ac9d3f14b2d5050928483d3d3b8fb6b4018893fc75710e6aa361ecb2474", size = 213036 },
    { url = "https://files.pythonhosted.org/packages/07/94/2d7d1e328f45ff34a0a284cf5a2847013701e24c2a53117e7c280a4316b3/propcache-0.3.2-cp312-cp312-musllinux_1_2_ppc64le.whl", hash = "sha256:0b504d29f3c47cf6b9e936c1852246c83d450e8e063d50562115a6be6d3a2535", size = 227684 },
    { url = "https://files.pythonhosted.org/packages/b7/05/37ae63a0087677e90b1d14710e532ff104d44bc1efa3b3970fff99b891dc/propcache-0.3.2-cp312-cp312-musllinux_1_2_s390x.whl", hash = "sha256:ce2ac2675a6aa41ddb2a0c9cbff53780a617ac3d43e620f8fd77ba1c84dcfc06", size = 234562 },
    { url = "https://files.pythonhosted.org/packages/a4/7c/3f539fcae630408d0bd8bf3208b9a647ccad10976eda62402a80adf8fc34/propcache-0.3.2-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:62b4239611205294cc433845b914131b2a1f03500ff3c1ed093ed216b82621e1", size = 222142 },
    { url = "https://files.pythonhosted.org/packages/7c/d2/34b9eac8c35f79f8a962546b3e97e9d4b990c420ee66ac8255d5d9611648/propcache-0.3.2-cp312-cp312-win32.whl", hash = "sha256:df4a81b9b53449ebc90cc4deefb052c1dd934ba85012aa912c7ea7b7e38b60c1", size = 37711 },
    { url = "https://files.pythonhosted.org/packages/19/61/d582be5d226cf79071681d1b46b848d6cb03d7b70af7063e33a2787eaa03/propcache-0.3.2-cp312-cp312-win_amd64.whl", hash = "sha256:7046e79b989d7fe457bb755844019e10f693752d169076138abf17f31380800c", size = 41479 },
    { url = "https://files.pythonhosted.org/packages/dc/d1/8c747fafa558c603c4ca19d8e20b288aa0c7cda74e9402f50f31eb65267e/propcache-0.3.2-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:ca592ed634a73ca002967458187109265e980422116c0a107cf93d81f95af945", size = 71286 },
    { url = "https://files.pythonhosted.org/packages/61/99/d606cb7986b60d89c36de8a85d58764323b3a5ff07770a99d8e993b3fa73/propcache-0.3.2-cp313-cp313-macosx_10_13_x86_64.whl", hash = "sha256:9ecb0aad4020e275652ba3975740f241bd12a61f1a784df044cf7477a02bc252", size = 42425 },
    { url = "https://files.pythonhosted.org/packages/8c/96/ef98f91bbb42b79e9bb82bdd348b255eb9d65f14dbbe3b1594644c4073f7/propcache-0.3.2-cp313-cp313-macosx_11_0_arm64.whl", hash = "sha256:7f08f1cc28bd2eade7a8a3d2954ccc673bb02062e3e7da09bc75d843386b342f", size = 41846 },
    { url = "https://files.pythonhosted.org/packages/5b/ad/3f0f9a705fb630d175146cd7b1d2bf5555c9beaed54e94132b21aac098a6/propcache-0.3.2-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:d1a342c834734edb4be5ecb1e9fb48cb64b1e2320fccbd8c54bf8da8f2a84c33", size = 208871 },
    { url = "https://files.pythonhosted.org/packages/3a/38/2085cda93d2c8b6ec3e92af2c89489a36a5886b712a34ab25de9fbca7992/propcache-0.3.2-cp313-cp313-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:8a544caaae1ac73f1fecfae70ded3e93728831affebd017d53449e3ac052ac1e", size = 215720 },
    { url = "https://files.pythonhosted.org/packages/61/c1/d72ea2dc83ac7f2c8e182786ab0fc2c7bd123a1ff9b7975bee671866fe5f/propcache-0.3.2-cp313-cp313-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:310d11aa44635298397db47a3ebce7db99a4cc4b9bbdfcf6c98a60c8d5261cf1", size = 215203 },
    { url = "https://files.pythonhosted.org/packages/af/81/b324c44ae60c56ef12007105f1460d5c304b0626ab0cc6b07c8f2a9aa0b8/propcache-0.3.2-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:4c1396592321ac83157ac03a2023aa6cc4a3cc3cfdecb71090054c09e5a7cce3", size = 206365 },
    { url = "https://files.pythonhosted.org/packages/09/73/88549128bb89e66d2aff242488f62869014ae092db63ccea53c1cc75a81d/propcache-0.3.2-cp313-cp313-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:8cabf5b5902272565e78197edb682017d21cf3b550ba0460ee473753f28d23c1", size = 196016 },
    { url = "https://files.pythonhosted.org/packages/b9/3f/3bdd14e737d145114a5eb83cb172903afba7242f67c5877f9909a20d948d/propcache-0.3.2-cp313-cp313-musllinux_1_2_aarch64.whl", hash = "sha256:0a2f2235ac46a7aa25bdeb03a9e7060f6ecbd213b1f9101c43b3090ffb971ef6", size = 205596 },
    { url = "https://files.pythonhosted.org/packages/0f/ca/2f4aa819c357d3107c3763d7ef42c03980f9ed5c48c82e01e25945d437c1/propcache-0.3.2-cp313-cp313-musllinux_1_2_armv7l.whl", hash = "sha256:92b69e12e34869a6970fd2f3da91669899994b47c98f5d430b781c26f1d9f387", size = 200977 },
    { url = "https://files.pythonhosted.org/packages/cd/4a/e65276c7477533c59085251ae88505caf6831c0e85ff8b2e31ebcbb949b1/propcache-0.3.2-cp313-cp313-musllinux_1_2_i686.whl", hash = "sha256:54e02207c79968ebbdffc169591009f4474dde3b4679e16634d34c9363ff56b4", size = 197220 },
    { url = "https://files.pythonhosted.org/packages/7c/54/fc7152e517cf5578278b242396ce4d4b36795423988ef39bb8cd5bf274c8/propcache-0.3.2-cp313-cp313-musllinux_1_2_ppc64le.whl", hash = "sha256:4adfb44cb588001f68c5466579d3f1157ca07f7504fc91ec87862e2b8e556b88", size = 210642 },
    { url = "https://files.pythonhosted.org/packages/b9/80/abeb4a896d2767bf5f1ea7b92eb7be6a5330645bd7fb844049c0e4045d9d/propcache-0.3.2-cp313-cp313-musllinux_1_2_s390x.whl", hash = "sha256:fd3e6019dc1261cd0291ee8919dd91fbab7b169bb76aeef6c716833a3f65d206", size = 212789 },
    { url = "https://files.pythonhosted.org/packages/b3/db/ea12a49aa7b2b6d68a5da8293dcf50068d48d088100ac016ad92a6a780e6/propcache-0.3.2-cp313-cp313-musllinux_1_2_x86_64.whl", hash = "sha256:4c181cad81158d71c41a2bce88edce078458e2dd5ffee7eddd6b05da85079f43", size = 205880 },
    { url = "https://files.pythonhosted.org/packages/d1/e5/9076a0bbbfb65d1198007059c65639dfd56266cf8e477a9707e4b1999ff4/propcache-0.3.2-cp313-cp313-win32.whl", hash = "sha256:8a08154613f2249519e549de2330cf8e2071c2887309a7b07fb56098f5170a02", size = 37220 },
    { url = "https://files.pythonhosted.org/packages/d3/f5/b369e026b09a26cd77aa88d8fffd69141d2ae00a2abaaf5380d2603f4b7f/propcache-0.3.2-cp313-cp313-win_amd64.whl", hash = "sha256:e41671f1594fc4ab0a6dec1351864713cb3a279910ae8b58f884a88a0a632c05", size = 40678 },
    { url = "https://files.pythonhosted.org/packages/a4/3a/6ece377b55544941a08d03581c7bc400a3c8cd3c2865900a68d5de79e21f/propcache-0.3.2-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:9a3cf035bbaf035f109987d9d55dc90e4b0e36e04bbbb95af3055ef17194057b", size = 76560 },
    { url = "https://files.pythonhosted.org/packages/0c/da/64a2bb16418740fa634b0e9c3d29edff1db07f56d3546ca2d86ddf0305e1/propcache-0.3.2-cp313-cp313t-macosx_10_13_x86_64.whl", hash = "sha256:156c03d07dc1323d8dacaa221fbe028c5c70d16709cdd63502778e6c3ccca1b0", size = 44676 },
    { url = "https://files.pythonhosted.org/packages/36/7b/f025e06ea51cb72c52fb87e9b395cced02786610b60a3ed51da8af017170/propcache-0.3.2-cp313-cp313t-macosx_11_0_arm64.whl", hash = "sha256:74413c0ba02ba86f55cf60d18daab219f7e531620c15f1e23d95563f505efe7e", size = 44701 },
    { url = "https://files.pythonhosted.org/packages/a4/00/faa1b1b7c3b74fc277f8642f32a4c72ba1d7b2de36d7cdfb676db7f4303e/propcache-0.3.2-cp313-cp313t-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:f066b437bb3fa39c58ff97ab2ca351db465157d68ed0440abecb21715eb24b28", size = 276934 },
    { url = "https://files.pythonhosted.org/packages/74/ab/935beb6f1756e0476a4d5938ff44bf0d13a055fed880caf93859b4f1baf4/propcache-0.3.2-cp313-cp313t-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:f1304b085c83067914721e7e9d9917d41ad87696bf70f0bc7dee450e9c71ad0a", size = 278316 },
    { url = "https://files.pythonhosted.org/packages/f8/9d/994a5c1ce4389610838d1caec74bdf0e98b306c70314d46dbe4fcf21a3e2/propcache-0.3.2-cp313-cp313t-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:ab50cef01b372763a13333b4e54021bdcb291fc9a8e2ccb9c2df98be51bcde6c", size = 282619 },
    { url = "https://files.pythonhosted.org/packages/2b/00/a10afce3d1ed0287cef2e09506d3be9822513f2c1e96457ee369adb9a6cd/propcache-0.3.2-cp313-cp313t-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:fad3b2a085ec259ad2c2842666b2a0a49dea8463579c606426128925af1ed725", size = 265896 },
    { url = "https://files.pythonhosted.org/packages/2e/a8/2aa6716ffa566ca57c749edb909ad27884680887d68517e4be41b02299f3/propcache-0.3.2-cp313-cp313t-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:261fa020c1c14deafd54c76b014956e2f86991af198c51139faf41c4d5e83892", size = 252111 },
    { url = "https://files.pythonhosted.org/packages/36/4f/345ca9183b85ac29c8694b0941f7484bf419c7f0fea2d1e386b4f7893eed/propcache-0.3.2-cp313-cp313t-musllinux_1_2_aarch64.whl", hash = "sha256:46d7f8aa79c927e5f987ee3a80205c987717d3659f035c85cf0c3680526bdb44", size = 268334 },
    { url = "https://files.pythonhosted.org/packages/3e/ca/fcd54f78b59e3f97b3b9715501e3147f5340167733d27db423aa321e7148/propcache-0.3.2-cp313-cp313t-musllinux_1_2_armv7l.whl", hash = "sha256:6d8f3f0eebf73e3c0ff0e7853f68be638b4043c65a70517bb575eff54edd8dbe", size = 255026 },
    { url = "https://files.pythonhosted.org/packages/8b/95/8e6a6bbbd78ac89c30c225210a5c687790e532ba4088afb8c0445b77ef37/propcache-0.3.2-cp313-cp313t-musllinux_1_2_i686.whl", hash = "sha256:03c89c1b14a5452cf15403e291c0ccd7751d5b9736ecb2c5bab977ad6c5bcd81", size = 250724 },
    { url = "https://files.pythonhosted.org/packages/ee/b0/0dd03616142baba28e8b2d14ce5df6631b4673850a3d4f9c0f9dd714a404/propcache-0.3.2-cp313-cp313t-musllinux_1_2_ppc64le.whl", hash = "sha256:0cc17efde71e12bbaad086d679ce575268d70bc123a5a71ea7ad76f70ba30bba", size = 268868 },
    { url = "https://files.pythonhosted.org/packages/c5/98/2c12407a7e4fbacd94ddd32f3b1e3d5231e77c30ef7162b12a60e2dd5ce3/propcache-0.3.2-cp313-cp313t-musllinux_1_2_s390x.whl", hash = "sha256:acdf05d00696bc0447e278bb53cb04ca72354e562cf88ea6f9107df8e7fd9770", size = 271322 },
    { url = "https://files.pythonhosted.org/packages/35/91/9cb56efbb428b006bb85db28591e40b7736847b8331d43fe335acf95f6c8/propcache-0.3.2-cp313-cp313t-musllinux_1_2_x86_64.whl", hash = "sha256:4445542398bd0b5d32df908031cb1b30d43ac848e20470a878b770ec2dcc6330", size = 265778 },
    { url = "https://files.pythonhosted.org/packages/9a/4c/b0fe775a2bdd01e176b14b574be679d84fc83958335790f7c9a686c1f468/propcache-0.3.2-cp313-cp313t-win32.whl", hash = "sha256:f86e5d7cd03afb3a1db8e9f9f6eff15794e79e791350ac48a8c924e6f439f394", size = 41175 },
    { url = "https://files.pythonhosted.org/packages/a4/ff/47f08595e3d9b5e149c150f88d9714574f1a7cbd89fe2817158a952674bf/propcache-0.3.2-cp313-cp313t-win_amd64.whl", hash = "sha256:9704bedf6e7cbe3c65eca4379a9b53ee6a83749f047808cbb5044d40d7d72198", size = 44857 },
    { url = "https://files.pythonhosted.org/packages/cc/35/cc0aaecf278bb4575b8555f2b137de5ab821595ddae9da9d3cd1da4072c7/propcache-0.3.2-py3-none-any.whl", hash = "sha256:98f1ec44fb675f5052cccc8e609c46ed23a35a1cfd18545ad4e29002d858a43f", size = 12663 },
]

[[package]]
name = "pydantic"
version = "2.11.7"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "annotated-types" },
    { name = "pydantic-core" },
    { name = "typing-extensions" },
    { name = "typing-inspection" },
]
sdist = { url = "https://files.pythonhosted.org/packages/00/dd/4325abf92c39ba8623b5af936ddb36ffcfe0beae70405d456ab1fb2f5b8c/pydantic-2.11.7.tar.gz", hash = "sha256:d989c3c6cb79469287b1569f7447a17848c998458d49ebe294e975b9baf0f0db", size = 788350 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/6a/c0/ec2b1c8712ca690e5d61979dee872603e92b8a32f94cc1b72d53beab008a/pydantic-2.11.7-py3-none-any.whl", hash = "sha256:dde5df002701f6de26248661f6835bbe296a47bf73990135c7d07ce741b9623b", size = 444782 },
]

[[package]]
name = "pydantic-core"
version = "2.33.2"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "typing-extensions" },
]
sdist = { url = "https://files.pythonhosted.org/packages/ad/88/5f2260bdfae97aabf98f1778d43f69574390ad787afb646292a638c923d4/pydantic_core-2.33.2.tar.gz", hash = "sha256:7cb8bc3605c29176e1b105350d2e6474142d7c1bd1d9327c4a9bdb46bf827acc", size = 435195 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/18/8a/2b41c97f554ec8c71f2a8a5f85cb56a8b0956addfe8b0efb5b3d77e8bdc3/pydantic_core-2.33.2-cp312-cp312-macosx_10_12_x86_64.whl", hash = "sha256:a7ec89dc587667f22b6a0b6579c249fca9026ce7c333fc142ba42411fa243cdc", size = 2009000 },
    { url = "https://files.pythonhosted.org/packages/a1/02/6224312aacb3c8ecbaa959897af57181fb6cf3a3d7917fd44d0f2917e6f2/pydantic_core-2.33.2-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:3c6db6e52c6d70aa0d00d45cdb9b40f0433b96380071ea80b09277dba021ddf7", size = 1847996 },
    { url = "https://files.pythonhosted.org/packages/d6/46/6dcdf084a523dbe0a0be59d054734b86a981726f221f4562aed313dbcb49/pydantic_core-2.33.2-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:4e61206137cbc65e6d5256e1166f88331d3b6238e082d9f74613b9b765fb9025", size = 1880957 },
    { url = "https://files.pythonhosted.org/packages/ec/6b/1ec2c03837ac00886ba8160ce041ce4e325b41d06a034adbef11339ae422/pydantic_core-2.33.2-cp312-cp312-manylinux_2_17_armv7l.manylinux2014_armv7l.whl", hash = "sha256:eb8c529b2819c37140eb51b914153063d27ed88e3bdc31b71198a198e921e011", size = 1964199 },
    { url = "https://files.pythonhosted.org/packages/2d/1d/6bf34d6adb9debd9136bd197ca72642203ce9aaaa85cfcbfcf20f9696e83/pydantic_core-2.33.2-cp312-cp312-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:c52b02ad8b4e2cf14ca7b3d918f3eb0ee91e63b3167c32591e57c4317e134f8f", size = 2120296 },
    { url = "https://files.pythonhosted.org/packages/e0/94/2bd0aaf5a591e974b32a9f7123f16637776c304471a0ab33cf263cf5591a/pydantic_core-2.33.2-cp312-cp312-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:96081f1605125ba0855dfda83f6f3df5ec90c61195421ba72223de35ccfb2f88", size = 2676109 },
    { url = "https://files.pythonhosted.org/packages/f9/41/4b043778cf9c4285d59742281a769eac371b9e47e35f98ad321349cc5d61/pydantic_core-2.33.2-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:8f57a69461af2a5fa6e6bbd7a5f60d3b7e6cebb687f55106933188e79ad155c1", size = 2002028 },
    { url = "https://files.pythonhosted.org/packages/cb/d5/7bb781bf2748ce3d03af04d5c969fa1308880e1dca35a9bd94e1a96a922e/pydantic_core-2.33.2-cp312-cp312-manylinux_2_5_i686.manylinux1_i686.whl", hash = "sha256:572c7e6c8bb4774d2ac88929e3d1f12bc45714ae5ee6d9a788a9fb35e60bb04b", size = 2100044 },
    { url = "https://files.pythonhosted.org/packages/fe/36/def5e53e1eb0ad896785702a5bbfd25eed546cdcf4087ad285021a90ed53/pydantic_core-2.33.2-cp312-cp312-musllinux_1_1_aarch64.whl", hash = "sha256:db4b41f9bd95fbe5acd76d89920336ba96f03e149097365afe1cb092fceb89a1", size = 2058881 },
    { url = "https://files.pythonhosted.org/packages/01/6c/57f8d70b2ee57fc3dc8b9610315949837fa8c11d86927b9bb044f8705419/pydantic_core-2.33.2-cp312-cp312-musllinux_1_1_armv7l.whl", hash = "sha256:fa854f5cf7e33842a892e5c73f45327760bc7bc516339fda888c75ae60edaeb6", size = 2227034 },
    { url = "https://files.pythonhosted.org/packages/27/b9/9c17f0396a82b3d5cbea4c24d742083422639e7bb1d5bf600e12cb176a13/pydantic_core-2.33.2-cp312-cp312-musllinux_1_1_x86_64.whl", hash = "sha256:5f483cfb75ff703095c59e365360cb73e00185e01aaea067cd19acffd2ab20ea", size = 2234187 },
    { url = "https://files.pythonhosted.org/packages/b0/6a/adf5734ffd52bf86d865093ad70b2ce543415e0e356f6cacabbc0d9ad910/pydantic_core-2.33.2-cp312-cp312-win32.whl", hash = "sha256:9cb1da0f5a471435a7bc7e439b8a728e8b61e59784b2af70d7c169f8dd8ae290", size = 1892628 },
    { url = "https://files.pythonhosted.org/packages/43/e4/5479fecb3606c1368d496a825d8411e126133c41224c1e7238be58b87d7e/pydantic_core-2.33.2-cp312-cp312-win_amd64.whl", hash = "sha256:f941635f2a3d96b2973e867144fde513665c87f13fe0e193c158ac51bfaaa7b2", size = 1955866 },
    { url = "https://files.pythonhosted.org/packages/0d/24/8b11e8b3e2be9dd82df4b11408a67c61bb4dc4f8e11b5b0fc888b38118b5/pydantic_core-2.33.2-cp312-cp312-win_arm64.whl", hash = "sha256:cca3868ddfaccfbc4bfb1d608e2ccaaebe0ae628e1416aeb9c4d88c001bb45ab", size = 1888894 },
    { url = "https://files.pythonhosted.org/packages/46/8c/99040727b41f56616573a28771b1bfa08a3d3fe74d3d513f01251f79f172/pydantic_core-2.33.2-cp313-cp313-macosx_10_12_x86_64.whl", hash = "sha256:1082dd3e2d7109ad8b7da48e1d4710c8d06c253cbc4a27c1cff4fbcaa97a9e3f", size = 2015688 },
    { url = "https://files.pythonhosted.org/packages/3a/cc/5999d1eb705a6cefc31f0b4a90e9f7fc400539b1a1030529700cc1b51838/pydantic_core-2.33.2-cp313-cp313-macosx_11_0_arm64.whl", hash = "sha256:f517ca031dfc037a9c07e748cefd8d96235088b83b4f4ba8939105d20fa1dcd6", size = 1844808 },
    { url = "https://files.pythonhosted.org/packages/6f/5e/a0a7b8885c98889a18b6e376f344da1ef323d270b44edf8174d6bce4d622/pydantic_core-2.33.2-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:0a9f2c9dd19656823cb8250b0724ee9c60a82f3cdf68a080979d13092a3b0fef", size = 1885580 },
    { url = "https://files.pythonhosted.org/packages/3b/2a/953581f343c7d11a304581156618c3f592435523dd9d79865903272c256a/pydantic_core-2.33.2-cp313-cp313-manylinux_2_17_armv7l.manylinux2014_armv7l.whl", hash = "sha256:2b0a451c263b01acebe51895bfb0e1cc842a5c666efe06cdf13846c7418caa9a", size = 1973859 },
    { url = "https://files.pythonhosted.org/packages/e6/55/f1a813904771c03a3f97f676c62cca0c0a4138654107c1b61f19c644868b/pydantic_core-2.33.2-cp313-cp313-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:1ea40a64d23faa25e62a70ad163571c0b342b8bf66d5fa612ac0dec4f069d916", size = 2120810 },
    { url = "https://files.pythonhosted.org/packages/aa/c3/053389835a996e18853ba107a63caae0b9deb4a276c6b472931ea9ae6e48/pydantic_core-2.33.2-cp313-cp313-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:0fb2d542b4d66f9470e8065c5469ec676978d625a8b7a363f07d9a501a9cb36a", size = 2676498 },
    { url = "https://files.pythonhosted.org/packages/eb/3c/f4abd740877a35abade05e437245b192f9d0ffb48bbbbd708df33d3cda37/pydantic_core-2.33.2-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:9fdac5d6ffa1b5a83bca06ffe7583f5576555e6c8b3a91fbd25ea7780f825f7d", size = 2000611 },
    { url = "https://files.pythonhosted.org/packages/59/a7/63ef2fed1837d1121a894d0ce88439fe3e3b3e48c7543b2a4479eb99c2bd/pydantic_core-2.33.2-cp313-cp313-manylinux_2_5_i686.manylinux1_i686.whl", hash = "sha256:04a1a413977ab517154eebb2d326da71638271477d6ad87a769102f7c2488c56", size = 2107924 },
    { url = "https://files.pythonhosted.org/packages/04/8f/2551964ef045669801675f1cfc3b0d74147f4901c3ffa42be2ddb1f0efc4/pydantic_core-2.33.2-cp313-cp313-musllinux_1_1_aarch64.whl", hash = "sha256:c8e7af2f4e0194c22b5b37205bfb293d166a7344a5b0d0eaccebc376546d77d5", size = 2063196 },
    { url = "https://files.pythonhosted.org/packages/26/bd/d9602777e77fc6dbb0c7db9ad356e9a985825547dce5ad1d30ee04903918/pydantic_core-2.33.2-cp313-cp313-musllinux_1_1_armv7l.whl", hash = "sha256:5c92edd15cd58b3c2d34873597a1e20f13094f59cf88068adb18947df5455b4e", size = 2236389 },
    { url = "https://files.pythonhosted.org/packages/42/db/0e950daa7e2230423ab342ae918a794964b053bec24ba8af013fc7c94846/pydantic_core-2.33.2-cp313-cp313-musllinux_1_1_x86_64.whl", hash = "sha256:65132b7b4a1c0beded5e057324b7e16e10910c106d43675d9bd87d4f38dde162", size = 2239223 },
    { url = "https://files.pythonhosted.org/packages/58/4d/4f937099c545a8a17eb52cb67fe0447fd9a373b348ccfa9a87f141eeb00f/pydantic_core-2.33.2-cp313-cp313-win32.whl", hash = "sha256:52fb90784e0a242bb96ec53f42196a17278855b0f31ac7c3cc6f5c1ec4811849", size = 1900473 },
    { url = "https://files.pythonhosted.org/packages/a0/75/4a0a9bac998d78d889def5e4ef2b065acba8cae8c93696906c3a91f310ca/pydantic_core-2.33.2-cp313-cp313-win_amd64.whl", hash = "sha256:c083a3bdd5a93dfe480f1125926afcdbf2917ae714bdb80b36d34318b2bec5d9", size = 1955269 },
    { url = "https://files.pythonhosted.org/packages/f9/86/1beda0576969592f1497b4ce8e7bc8cbdf614c352426271b1b10d5f0aa64/pydantic_core-2.33.2-cp313-cp313-win_arm64.whl", hash = "sha256:e80b087132752f6b3d714f041ccf74403799d3b23a72722ea2e6ba2e892555b9", size = 1893921 },
    { url = "https://files.pythonhosted.org/packages/a4/7d/e09391c2eebeab681df2b74bfe6c43422fffede8dc74187b2b0bf6fd7571/pydantic_core-2.33.2-cp313-cp313t-macosx_11_0_arm64.whl", hash = "sha256:61c18fba8e5e9db3ab908620af374db0ac1baa69f0f32df4f61ae23f15e586ac", size = 1806162 },
    { url = "https://files.pythonhosted.org/packages/f1/3d/847b6b1fed9f8ed3bb95a9ad04fbd0b212e832d4f0f50ff4d9ee5a9f15cf/pydantic_core-2.33.2-cp313-cp313t-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:95237e53bb015f67b63c91af7518a62a8660376a6a0db19b89acc77a4d6199f5", size = 1981560 },
    { url = "https://files.pythonhosted.org/packages/6f/9a/e73262f6c6656262b5fdd723ad90f518f579b7bc8622e43a942eec53c938/pydantic_core-2.33.2-cp313-cp313t-win_amd64.whl", hash = "sha256:c2fc0a768ef76c15ab9238afa6da7f69895bb5d1ee83aeea2e3509af4472d0b9", size = 1935777 },
]

[[package]]
name = "pygments"
version = "2.19.2"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/b0/77/a5b8c569bf593b0140bde72ea885a803b82086995367bf2037de0159d924/pygments-2.19.2.tar.gz", hash = "sha256:636cb2477cec7f8952536970bc533bc43743542f70392ae026374600add5b887", size = 4968631 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/c7/21/705964c7812476f378728bdf590ca4b771ec72385c533964653c68e86bdc/pygments-2.19.2-py3-none-any.whl", hash = "sha256:86540386c03d588bb81d44bc3928634ff26449851e99741617ecb9037ee5ec0b", size = 1225217 },
]

[[package]]
name = "pyobjc"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-accessibility", marker = "platform_release >= '20.0'" },
    { name = "pyobjc-framework-accounts", marker = "platform_release >= '12.0'" },
    { name = "pyobjc-framework-addressbook" },
    { name = "pyobjc-framework-adservices", marker = "platform_release >= '20.0'" },
    { name = "pyobjc-framework-adsupport", marker = "platform_release >= '18.0'" },
    { name = "pyobjc-framework-applescriptkit" },
    { name = "pyobjc-framework-applescriptobjc", marker = "platform_release >= '10.0'" },
    { name = "pyobjc-framework-applicationservices" },
    { name = "pyobjc-framework-apptrackingtransparency", marker = "platform_release >= '20.0'" },
    { name = "pyobjc-framework-audiovideobridging", marker = "platform_release >= '12.0'" },
    { name = "pyobjc-framework-authenticationservices", marker = "platform_release >= '19.0'" },
    { name = "pyobjc-framework-automaticassessmentconfiguration", marker = "platform_release >= '19.0'" },
    { name = "pyobjc-framework-automator" },
    { name = "pyobjc-framework-avfoundation", marker = "platform_release >= '11.0'" },
    { name = "pyobjc-framework-avkit", marker = "platform_release >= '13.0'" },
    { name = "pyobjc-framework-avrouting", marker = "platform_release >= '22.0'" },
    { name = "pyobjc-framework-backgroundassets", marker = "platform_release >= '22.0'" },
    { name = "pyobjc-framework-browserenginekit", marker = "platform_release >= '23.4'" },
    { name = "pyobjc-framework-businesschat", marker = "platform_release >= '18.0'" },
    { name = "pyobjc-framework-calendarstore", marker = "platform_release >= '9.0'" },
    { name = "pyobjc-framework-callkit", marker = "platform_release >= '20.0'" },
    { name = "pyobjc-framework-carbon" },
    { name = "pyobjc-framework-cfnetwork" },
    { name = "pyobjc-framework-cinematic", marker = "platform_release >= '23.0'" },
    { name = "pyobjc-framework-classkit", marker = "platform_release >= '20.0'" },
    { name = "pyobjc-framework-cloudkit", marker = "platform_release >= '14.0'" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-collaboration", marker = "platform_release >= '9.0'" },
    { name = "pyobjc-framework-colorsync", marker = "platform_release >= '17.0'" },
    { name = "pyobjc-framework-contacts", marker = "platform_release >= '15.0'" },
    { name = "pyobjc-framework-contactsui", marker = "platform_release >= '15.0'" },
    { name = "pyobjc-framework-coreaudio" },
    { name = "pyobjc-framework-coreaudiokit" },
    { name = "pyobjc-framework-corebluetooth", marker = "platform_release >= '14.0'" },
    { name = "pyobjc-framework-coredata" },
    { name = "pyobjc-framework-corehaptics", marker = "platform_release >= '19.0'" },
    { name = "pyobjc-framework-corelocation", marker = "platform_release >= '10.0'" },
    { name = "pyobjc-framework-coremedia", marker = "platform_release >= '11.0'" },
    { name = "pyobjc-framework-coremediaio", marker = "platform_release >= '11.0'" },
    { name = "pyobjc-framework-coremidi" },
    { name = "pyobjc-framework-coreml", marker = "platform_release >= '17.0'" },
    { name = "pyobjc-framework-coremotion", marker = "platform_release >= '19.0'" },
    { name = "pyobjc-framework-coreservices" },
    { name = "pyobjc-framework-corespotlight", marker = "platform_release >= '17.0'" },
    { name = "pyobjc-framework-coretext" },
    { name = "pyobjc-framework-corewlan", marker = "platform_release >= '10.0'" },
    { name = "pyobjc-framework-cryptotokenkit", marker = "platform_release >= '14.0'" },
    { name = "pyobjc-framework-datadetection", marker = "platform_release >= '21.0'" },
    { name = "pyobjc-framework-devicecheck", marker = "platform_release >= '19.0'" },
    { name = "pyobjc-framework-devicediscoveryextension", marker = "platform_release >= '24.0'" },
    { name = "pyobjc-framework-dictionaryservices", marker = "platform_release >= '9.0'" },
    { name = "pyobjc-framework-discrecording" },
    { name = "pyobjc-framework-discrecordingui" },
    { name = "pyobjc-framework-diskarbitration" },
    { name = "pyobjc-framework-dvdplayback" },
    { name = "pyobjc-framework-eventkit", marker = "platform_release >= '12.0'" },
    { name = "pyobjc-framework-exceptionhandling" },
    { name = "pyobjc-framework-executionpolicy", marker = "platform_release >= '19.0'" },
    { name = "pyobjc-framework-extensionkit", marker = "platform_release >= '22.0'" },
    { name = "pyobjc-framework-externalaccessory", marker = "platform_release >= '17.0'" },
    { name = "pyobjc-framework-fileprovider", marker = "platform_release >= '19.0'" },
    { name = "pyobjc-framework-fileproviderui", marker = "platform_release >= '19.0'" },
    { name = "pyobjc-framework-findersync", marker = "platform_release >= '14.0'" },
    { name = "pyobjc-framework-fsevents", marker = "platform_release >= '9.0'" },
    { name = "pyobjc-framework-fskit", marker = "platform_release >= '24.4'" },
    { name = "pyobjc-framework-gamecenter", marker = "platform_release >= '12.0'" },
    { name = "pyobjc-framework-gamecontroller", marker = "platform_release >= '13.0'" },
    { name = "pyobjc-framework-gamekit", marker = "platform_release >= '12.0'" },
    { name = "pyobjc-framework-gameplaykit", marker = "platform_release >= '15.0'" },
    { name = "pyobjc-framework-healthkit", marker = "platform_release >= '22.0'" },
    { name = "pyobjc-framework-imagecapturecore", marker = "platform_release >= '10.0'" },
    { name = "pyobjc-framework-inputmethodkit", marker = "platform_release >= '9.0'" },
    { name = "pyobjc-framework-installerplugins" },
    { name = "pyobjc-framework-instantmessage", marker = "platform_release >= '9.0'" },
    { name = "pyobjc-framework-intents", marker = "platform_release >= '16.0'" },
    { name = "pyobjc-framework-intentsui", marker = "platform_release >= '21.0'" },
    { name = "pyobjc-framework-iobluetooth" },
    { name = "pyobjc-framework-iobluetoothui" },
    { name = "pyobjc-framework-iosurface", marker = "platform_release >= '10.0'" },
    { name = "pyobjc-framework-ituneslibrary", marker = "platform_release >= '10.0'" },
    { name = "pyobjc-framework-kernelmanagement", marker = "platform_release >= '20.0'" },
    { name = "pyobjc-framework-latentsemanticmapping" },
    { name = "pyobjc-framework-launchservices" },
    { name = "pyobjc-framework-libdispatch", marker = "platform_release >= '12.0'" },
    { name = "pyobjc-framework-libxpc", marker = "platform_release >= '12.0'" },
    { name = "pyobjc-framework-linkpresentation", marker = "platform_release >= '19.0'" },
    { name = "pyobjc-framework-localauthentication", marker = "platform_release >= '14.0'" },
    { name = "pyobjc-framework-localauthenticationembeddedui", marker = "platform_release >= '21.0'" },
    { name = "pyobjc-framework-mailkit", marker = "platform_release >= '21.0'" },
    { name = "pyobjc-framework-mapkit", marker = "platform_release >= '13.0'" },
    { name = "pyobjc-framework-mediaaccessibility", marker = "platform_release >= '13.0'" },
    { name = "pyobjc-framework-mediaextension", marker = "platform_release >= '24.0'" },
    { name = "pyobjc-framework-medialibrary", marker = "platform_release >= '13.0'" },
    { name = "pyobjc-framework-mediaplayer", marker = "platform_release >= '16.0'" },
    { name = "pyobjc-framework-mediatoolbox", marker = "platform_release >= '13.0'" },
    { name = "pyobjc-framework-metal", marker = "platform_release >= '15.0'" },
    { name = "pyobjc-framework-metalfx", marker = "platform_release >= '22.0'" },
    { name = "pyobjc-framework-metalkit", marker = "platform_release >= '15.0'" },
    { name = "pyobjc-framework-metalperformanceshaders", marker = "platform_release >= '17.0'" },
    { name = "pyobjc-framework-metalperformanceshadersgraph", marker = "platform_release >= '20.0'" },
    { name = "pyobjc-framework-metrickit", marker = "platform_release >= '21.0'" },
    { name = "pyobjc-framework-mlcompute", marker = "platform_release >= '20.0'" },
    { name = "pyobjc-framework-modelio", marker = "platform_release >= '15.0'" },
    { name = "pyobjc-framework-multipeerconnectivity", marker = "platform_release >= '14.0'" },
    { name = "pyobjc-framework-naturallanguage", marker = "platform_release >= '18.0'" },
    { name = "pyobjc-framework-netfs", marker = "platform_release >= '10.0'" },
    { name = "pyobjc-framework-network", marker = "platform_release >= '18.0'" },
    { name = "pyobjc-framework-networkextension", marker = "platform_release >= '15.0'" },
    { name = "pyobjc-framework-notificationcenter", marker = "platform_release >= '14.0'" },
    { name = "pyobjc-framework-opendirectory", marker = "platform_release >= '10.0'" },
    { name = "pyobjc-framework-osakit" },
    { name = "pyobjc-framework-oslog", marker = "platform_release >= '19.0'" },
    { name = "pyobjc-framework-passkit", marker = "platform_release >= '20.0'" },
    { name = "pyobjc-framework-pencilkit", marker = "platform_release >= '19.0'" },
    { name = "pyobjc-framework-phase", marker = "platform_release >= '21.0'" },
    { name = "pyobjc-framework-photos", marker = "platform_release >= '15.0'" },
    { name = "pyobjc-framework-photosui", marker = "platform_release >= '15.0'" },
    { name = "pyobjc-framework-preferencepanes" },
    { name = "pyobjc-framework-pushkit", marker = "platform_release >= '19.0'" },
    { name = "pyobjc-framework-quartz" },
    { name = "pyobjc-framework-quicklookthumbnailing", marker = "platform_release >= '19.0'" },
    { name = "pyobjc-framework-replaykit", marker = "platform_release >= '20.0'" },
    { name = "pyobjc-framework-safariservices", marker = "platform_release >= '16.0'" },
    { name = "pyobjc-framework-safetykit", marker = "platform_release >= '22.0'" },
    { name = "pyobjc-framework-scenekit", marker = "platform_release >= '11.0'" },
    { name = "pyobjc-framework-screencapturekit", marker = "platform_release >= '21.4'" },
    { name = "pyobjc-framework-screensaver" },
    { name = "pyobjc-framework-screentime", marker = "platform_release >= '20.0'" },
    { name = "pyobjc-framework-scriptingbridge", marker = "platform_release >= '9.0'" },
    { name = "pyobjc-framework-searchkit" },
    { name = "pyobjc-framework-security" },
    { name = "pyobjc-framework-securityfoundation" },
    { name = "pyobjc-framework-securityinterface" },
    { name = "pyobjc-framework-securityui", marker = "platform_release >= '24.4'" },
    { name = "pyobjc-framework-sensitivecontentanalysis", marker = "platform_release >= '23.0'" },
    { name = "pyobjc-framework-servicemanagement", marker = "platform_release >= '10.0'" },
    { name = "pyobjc-framework-sharedwithyou", marker = "platform_release >= '22.0'" },
    { name = "pyobjc-framework-sharedwithyoucore", marker = "platform_release >= '22.0'" },
    { name = "pyobjc-framework-shazamkit", marker = "platform_release >= '21.0'" },
    { name = "pyobjc-framework-social", marker = "platform_release >= '12.0'" },
    { name = "pyobjc-framework-soundanalysis", marker = "platform_release >= '19.0'" },
    { name = "pyobjc-framework-speech", marker = "platform_release >= '19.0'" },
    { name = "pyobjc-framework-spritekit", marker = "platform_release >= '13.0'" },
    { name = "pyobjc-framework-storekit", marker = "platform_release >= '11.0'" },
    { name = "pyobjc-framework-symbols", marker = "platform_release >= '23.0'" },
    { name = "pyobjc-framework-syncservices" },
    { name = "pyobjc-framework-systemconfiguration" },
    { name = "pyobjc-framework-systemextensions", marker = "platform_release >= '19.0'" },
    { name = "pyobjc-framework-threadnetwork", marker = "platform_release >= '22.0'" },
    { name = "pyobjc-framework-uniformtypeidentifiers", marker = "platform_release >= '20.0'" },
    { name = "pyobjc-framework-usernotifications", marker = "platform_release >= '18.0'" },
    { name = "pyobjc-framework-usernotificationsui", marker = "platform_release >= '20.0'" },
    { name = "pyobjc-framework-videosubscriberaccount", marker = "platform_release >= '18.0'" },
    { name = "pyobjc-framework-videotoolbox", marker = "platform_release >= '12.0'" },
    { name = "pyobjc-framework-virtualization", marker = "platform_release >= '20.0'" },
    { name = "pyobjc-framework-vision", marker = "platform_release >= '17.0'" },
    { name = "pyobjc-framework-webkit" },
]
sdist = { url = "https://files.pythonhosted.org/packages/db/5e/16bc372806790d295c76b5c7851767cc9ee3787b3e581f5d7cc44158e4e0/pyobjc-11.1.tar.gz", hash = "sha256:a71b14389657811d658526ba4d5faba4ef7eadbddcf9fe8bf4fb3a6261effba3", size = 11161 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/a9/32/ad08b45fc0ad9850054ffe66fb0cb2ff7af3d2007c192dda14cf9a3ea893/pyobjc-11.1-py3-none-any.whl", hash = "sha256:903f822cba40be53d408b8eaf834514937ec0b4e6af1c5ecc24fcb652812dd85", size = 4164 },
]

[[package]]
name = "pyobjc-core"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/e8/e9/0b85c81e2b441267bca707b5d89f56c2f02578ef8f3eafddf0e0c0b8848c/pyobjc_core-11.1.tar.gz", hash = "sha256:b63d4d90c5df7e762f34739b39cc55bc63dbcf9fb2fb3f2671e528488c7a87fe", size = 974602 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/c0/09/e83228e878e73bf756749939f906a872da54488f18d75658afa7f1abbab1/pyobjc_core-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:765b97dea6b87ec4612b3212258024d8496ea23517c95a1c5f0735f96b7fd529", size = 677985 },
    { url = "https://files.pythonhosted.org/packages/c5/24/12e4e2dae5f85fd0c0b696404ed3374ea6ca398e7db886d4f1322eb30799/pyobjc_core-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:18986f83998fbd5d3f56d8a8428b2f3e0754fd15cef3ef786ca0d29619024f2c", size = 676431 },
    { url = "https://files.pythonhosted.org/packages/f7/79/031492497624de4c728f1857181b06ce8c56444db4d49418fa459cba217c/pyobjc_core-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:8849e78cfe6595c4911fbba29683decfb0bf57a350aed8a43316976ba6f659d2", size = 719330 },
    { url = "https://files.pythonhosted.org/packages/ed/7d/6169f16a0c7ec15b9381f8bf33872baf912de2ef68d96c798ca4c6ee641f/pyobjc_core-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:8cb9ed17a8d84a312a6e8b665dd22393d48336ea1d8277e7ad20c19a38edf731", size = 667203 },
    { url = "https://files.pythonhosted.org/packages/49/0f/f5ab2b0e57430a3bec9a62b6153c0e79c05a30d77b564efdb9f9446eeac5/pyobjc_core-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:f2455683e807f8541f0d83fbba0f5d9a46128ab0d5cc83ea208f0bec759b7f96", size = 708807 },
]

[[package]]
name = "pyobjc-framework-accessibility"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://files.pythonhosted.org/packages/78/b4/10c16e9d48568a68da2f61866b19468d4ac7129c377d4b1333ee936ae5d0/pyobjc_framework_accessibility-11.1.tar.gz", hash = "sha256:c0fa5f1e00906ec002f582c7d3d80463a46d19f672bf5ec51144f819eeb40656", size = 45098 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/5d/bd/087d511e0ea356434399609a38e8819978943cbeaca3ca7cc5f35c93d0b2/pyobjc_framework_accessibility-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:a049b63b32514da68aaaeef0d6c00a125e0618e4042aa6dbe3867b74fb2a8b2b", size = 11158 },
    { url = "https://files.pythonhosted.org/packages/0e/1e/4095d683954401d5f7926827fd09f4d399a8923e0e66d386a8903c0950e0/pyobjc_framework_accessibility-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:fd5a03b731d1a2bbb2bf706b58889a5e82df82ac69210ec3245c7dc69e42a63a", size = 11177 },
    { url = "https://files.pythonhosted.org/packages/28/7f/63d88c16e87f07b7bfff2adc7e74dcb2739cc1aed2110d29489514c05afa/pyobjc_framework_accessibility-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:3496c55569a421ef3c98ea66fc0ebaf68c686ede5b26db0fdcb0b0ad4191a20b", size = 11356 },
    { url = "https://files.pythonhosted.org/packages/ee/bd/7062e8670f7636aed8d61bde807a458a21962585e9d352cd576631a5eb96/pyobjc_framework_accessibility-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:7c4124981a5d84b71464babb4babfbeb5bfab145bc75b6f3577bd046a9579226", size = 11246 },
    { url = "https://files.pythonhosted.org/packages/73/79/66e1500a49203931d5b18fd4ae2f40139c27063e6724536d803d07b5bc14/pyobjc_framework_accessibility-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:ea98239e339136e3d20d753afe7908006cf29567ba39b8e83ceda7c221e6aad1", size = 11438 },
]

[[package]]
name = "pyobjc-framework-accounts"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/12/45/ca21003f68ad0f13b5a9ac1761862ad2ddd83224b4314a2f7d03ca437c8d/pyobjc_framework_accounts-11.1.tar.gz", hash = "sha256:384fec156e13ff75253bb094339013f4013464f6dfd47e2f7de3e2ae7441c030", size = 17086 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/6d/db/fa1c4a964fb9f390af8fce1d82c053f9d4467ffe6acdaab464bb3220e673/pyobjc_framework_accounts-11.1-py2.py3-none-any.whl", hash = "sha256:9c3fe342be7b8e73cba735e5a38affbe349cf8bc19091aa4fd788eabf2074b72", size = 5117 },
]

[[package]]
name = "pyobjc-framework-addressbook"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/eb/d3/f5bb5c72be5c6e52224f43e23e5a44e86d2c35ee9af36939e5514c6c7a0f/pyobjc_framework_addressbook-11.1.tar.gz", hash = "sha256:ce2db3be4a3128bf79d5c41319a6d16b73754785ce75ac694d0d658c690922fc", size = 97609 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/c2/de/e1ba5f113c05b543a097040add795fa4b85fdd5ad850b56d83cd6ce8afff/pyobjc_framework_addressbook-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:fb3d0a710f8342a0c63a8e4caf64a044b4d7e42d6d242c8e1b54470238b938cb", size = 13173 },
    { url = "https://files.pythonhosted.org/packages/59/53/a0487a0fbc9134e69e29f18334d0b610c44578d753e8264ea1ac649f2839/pyobjc_framework_addressbook-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:411adf4874cc4343f2928a26fe4cb3673d2f5f73365b45cd3650aa7304a45e24", size = 13188 },
    { url = "https://files.pythonhosted.org/packages/81/07/1ca336107358ad526394a720598b8549f613ef1797350c764535f26e47bc/pyobjc_framework_addressbook-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:6735f297f0e5fd109fa77ca90cace57eb2e10eb65e3c15ccd249df2228030d3b", size = 13358 },
    { url = "https://files.pythonhosted.org/packages/96/f7/c5ca9d90b2f6c6c04df8c61f788c5667467d1c63b8ccb85521eab9d463f7/pyobjc_framework_addressbook-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:e4004bdf134a069c58d91b231cbeb9e0adad26a73d2689015baaf6a98c411c54", size = 13228 },
    { url = "https://files.pythonhosted.org/packages/6a/14/275315178d6fa10ebc51d9713580ed53b6df3b3773600cfaef6ca4aa9baf/pyobjc_framework_addressbook-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:6bc42832e85f418a9f978b7e001e219faf52cbb279a0df185115cd4292c381cb", size = 13396 },
]

[[package]]
name = "pyobjc-framework-adservices"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/2a/3f/af76eab6eee0a405a4fdee172e7181773040158476966ecd757b0a98bfc5/pyobjc_framework_adservices-11.1.tar.gz", hash = "sha256:44c72f8163705c9aa41baca938fdb17dde257639e5797e6a5c3a2b2d8afdade9", size = 12473 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/8e/11/a63a171ce86c25a6ae85ebff6a9ab92b0d0cb1fd66ddc7d7b0d803f36191/pyobjc_framework_adservices-11.1-py2.py3-none-any.whl", hash = "sha256:1744f59a75b2375e139c39f3e85658e62cd10cc0f12b158a80421f18734e9ffc", size = 3474 },
]

[[package]]
name = "pyobjc-framework-adsupport"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/7f/03/9c51edd964796a97def4e1433d76a128dd7059b685fb4366081bf4e292ba/pyobjc_framework_adsupport-11.1.tar.gz", hash = "sha256:78b9667c275785df96219d205bd4309731869c3298d0931e32aed83bede29096", size = 12556 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/7d/b8/ad895efb24311cab2b9d6f7f7f6a833b7f354f80fec606e6c7893da9349b/pyobjc_framework_adsupport-11.1-py2.py3-none-any.whl", hash = "sha256:c3e009612778948910d3a7135b9d77b9b7c06aab29d40957770834c083acf825", size = 3387 },
]

[[package]]
name = "pyobjc-framework-applescriptkit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/bc/63/1bcfcdca53bf5bba3a7b4d73d24232ae1721a378a32fd4ebc34a35549df2/pyobjc_framework_applescriptkit-11.1.tar.gz", hash = "sha256:477707352eaa6cc4a5f8c593759dc3227a19d5958481b1482f0d59394a4601c3", size = 12392 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/c6/0e/68ac4ce71e613697a087c262aefacc9ed54eaf0cf1d9ffcd89134bfdab9b/pyobjc_framework_applescriptkit-11.1-py2.py3-none-any.whl", hash = "sha256:e22cbc9d1a25a4a713f21aa94dd017c311186b02062fc7ffbde3009495fb0067", size = 4334 },
]

[[package]]
name = "pyobjc-framework-applescriptobjc"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/a3/27/687b55b575367df045879b786f358355e40e41f847968e557d0718a6c4a4/pyobjc_framework_applescriptobjc-11.1.tar.gz", hash = "sha256:c8a0ec975b64411a4f16a1280c5ea8dbe949fd361e723edd343102f0f95aba6e", size = 12445 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/2d/33/ceb6a512b41fbf3458b9a281997ebb3056cc354981215261f0a2bf7d15d6/pyobjc_framework_applescriptobjc-11.1-py2.py3-none-any.whl", hash = "sha256:ac22526fd1f0a3b07ac1d77f90046b77f10ec9549182114f2428ee1e96d3de2b", size = 4433 },
]

[[package]]
name = "pyobjc-framework-applicationservices"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-coretext" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://files.pythonhosted.org/packages/be/3f/b33ce0cecc3a42f6c289dcbf9ff698b0d9e85f5796db2e9cb5dadccffbb9/pyobjc_framework_applicationservices-11.1.tar.gz", hash = "sha256:03fcd8c0c600db98fa8b85eb7b3bc31491701720c795e3f762b54e865138bbaf", size = 224842 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/38/ec/46a5c710e2d7edf55105223c34fed5a7b7cc7aba7d00a3a7b0405d6a2d1a/pyobjc_framework_applicationservices-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:f4a85ccd78bab84f7f05ac65ff9be117839dfc09d48c39edd65c617ed73eb01c", size = 31056 },
    { url = "https://files.pythonhosted.org/packages/c4/06/c2a309e6f37bfa73a2a581d3301321b2033e25b249e2a01e417a3c34e799/pyobjc_framework_applicationservices-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:385a89f4d0838c97a331e247519d9e9745aa3f7427169d18570e3c664076a63c", size = 31072 },
    { url = "https://files.pythonhosted.org/packages/b4/5f/357bf498c27f1b4d48385860d8374b2569adc1522aabe32befd77089c070/pyobjc_framework_applicationservices-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:f480fab20f3005e559c9d06c9a3874a1f1c60dde52c6d28a53ab59b45e79d55f", size = 31335 },
    { url = "https://files.pythonhosted.org/packages/ab/b6/797fdd81399fe8251196f29a621ba3f3f04d5c579d95fd304489f5558202/pyobjc_framework_applicationservices-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:e8dee91c6a14fd042f98819dc0ac4a182e0e816282565534032f0e544bfab143", size = 31196 },
    { url = "https://files.pythonhosted.org/packages/68/45/47eba8d7cdf16d778240ed13fb405e8d712464170ed29d0463363a695194/pyobjc_framework_applicationservices-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:a0ce40a57a9b993793b6f72c4fd93f80618ef54a69d76a1da97b8360a2f3ffc5", size = 31446 },
]

[[package]]
name = "pyobjc-framework-apptrackingtransparency"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/49/68/7aa3afffd038dd6e5af764336bca734eb910121013ca71030457b61e5b99/pyobjc_framework_apptrackingtransparency-11.1.tar.gz", hash = "sha256:796cc5f83346c10973806cfb535d4200b894a5d2626ff2eeb1972d594d14fed4", size = 13135 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/21/37/22cc0293c911a98a49c5fc007b968d82797101dd06e89c4c3266564ff443/pyobjc_framework_apptrackingtransparency-11.1-py2.py3-none-any.whl", hash = "sha256:e25c3eae25d24ee8b523b7ecc4d2b07af37c7733444b80c4964071dea7b0cb19", size = 3862 },
]

[[package]]
name = "pyobjc-framework-audiovideobridging"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/c3/25/6c5a7b1443d30139cc722029880284ea9dfa575f0436471b9364fcd499f5/pyobjc_framework_audiovideobridging-11.1.tar.gz", hash = "sha256:12756b3aa35083b8ad5c9139b6a0e2f4792e217096b5bf6b702d499038203991", size = 72913 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/1d/69/3e8e3da4db835168d18155a2c90fcca441047fc9c2e021d2ea01b4c6eb8c/pyobjc_framework_audiovideobridging-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:591e80ff6973ea51a12f7c1a2e3fd59496633a51d5a1bf73f4fb989a43e23681", size = 11032 },
    { url = "https://files.pythonhosted.org/packages/0b/93/cf38f503f378e224a57f99f8ca7f044f2690221dc8deaf49b305a6ee439a/pyobjc_framework_audiovideobridging-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:30a12be3784f41e1c6b5ef532c08e73bae7071d9a036b26b1e36b919ee5b6f57", size = 11043 },
    { url = "https://files.pythonhosted.org/packages/cf/ed/b2804e0415429292fd2f891f29e57b5008a2ecebb7de83aa9b78281e9284/pyobjc_framework_audiovideobridging-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:3bef4383dc9233dbd9efc3817ce9c8fe8670c61d21a94de3c149e7f460245792", size = 11217 },
    { url = "https://files.pythonhosted.org/packages/a4/34/6a92d1795bf246222a6e3c993ae12f95b3453c1777ee564ef685b7c31260/pyobjc_framework_audiovideobridging-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:6159b94448af08c9b119eb6ecf3fdbc2b3348ad66fb99586f991939779e412ec", size = 11075 },
    { url = "https://files.pythonhosted.org/packages/33/7d/975b7d24b103e015f2289cc160ea01b47b43a242b6f69f0b23a19e38b8bc/pyobjc_framework_audiovideobridging-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:e466561bd9eb77be050aabead6ad7313a480d05389d9892e1db2cbc06ce1f475", size = 11248 },
]

[[package]]
name = "pyobjc-framework-authenticationservices"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/8f/b7/3e9ad0ed3625dc02e495615ea5dbf55ca95cbd25b3e31f25092f5caad640/pyobjc_framework_authenticationservices-11.1.tar.gz", hash = "sha256:8fd801cdb53d426b4e678b0a8529c005d0c44f5a17ccd7052a7c3a1a87caed6a", size = 115266 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/7e/2d/cbb5e88c3713fb68cda7d76d37737076c1653bf1ac95418c30d4b614f4be/pyobjc_framework_authenticationservices-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:6655dd53d9135ef85265a4297da5e7459ed7836973f2796027fdfbfd7f08e433", size = 20385 },
    { url = "https://files.pythonhosted.org/packages/53/ac/cfd8aed9fba6974f291b3beb198c7270e4a3cae9f1ff9600bd0e4c904ae9/pyobjc_framework_authenticationservices-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:364035d265129192e6906f7a94cbdf714d737b6b9f20e56bfe74d0007c8761b1", size = 20401 },
    { url = "https://files.pythonhosted.org/packages/58/37/949c2f06ea52d976ff7c2c52a58504456ae4cc4f6c681e65ea9fa448a676/pyobjc_framework_authenticationservices-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:e92bf7e829229fbecba4f7f649d3ae38760cf25aa9e909c0e737b1945f36b62d", size = 20636 },
    { url = "https://files.pythonhosted.org/packages/15/75/6372808569c763ea00ba393d4eaee5cf4f73fd4fd5b222042e1c0d2aac65/pyobjc_framework_authenticationservices-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:60bf585e561d885cc88a21713ef2db259baf6434ce7116f82265a0c727f29dba", size = 20574 },
    { url = "https://files.pythonhosted.org/packages/74/25/996581a175ce0394ee1abb76c4798478bc0ef32f55a78d4b49079b24fd78/pyobjc_framework_authenticationservices-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:f19ea757ecfda6ac929559c779c3afb001855dd5e41e4acc4c42343c7d912da6", size = 20822 },
]

[[package]]
name = "pyobjc-framework-automaticassessmentconfiguration"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/3d/39/d4c94e0245d290b83919854c4f205851cc0b2603f843448fdfb8e74aad71/pyobjc_framework_automaticassessmentconfiguration-11.1.tar.gz", hash = "sha256:70eadbf8600101901a56fcd7014d8941604e14f3b3728bc4fb0178a9a9420032", size = 24933 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/5e/e0/5a67f8ee0393447ca8251cbd06788cb7f3a1f4b9b052afd2e1b2cdfcb504/pyobjc_framework_automaticassessmentconfiguration-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:55d1684dd676730fb1afbc7c67e0669e3a7159f18c126fea7453fe6182c098f9", size = 9193 },
    { url = "https://files.pythonhosted.org/packages/58/04/e2fb203d36b7ec96b06ef26cb44b833d64195435bc5d879987238111b524/pyobjc_framework_automaticassessmentconfiguration-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:fbcbe406c2a02d632885f6b23285c259b715f019b938d666cc554a66ecf5f9c3", size = 9199 },
    { url = "https://files.pythonhosted.org/packages/03/d7/bd947463be8b6f1512a99cb605a57a52f960bb70da060e21a23131a55386/pyobjc_framework_automaticassessmentconfiguration-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:e5fa297c7d4db225f75e5d11121fa68e0956c104e14b24250a52157a180e5f6c", size = 9359 },
    { url = "https://files.pythonhosted.org/packages/bf/72/b4674dc09acc106be130737b0d18f17ba0b5b72728d52bc951511d4067c0/pyobjc_framework_automaticassessmentconfiguration-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:4b11c33fb6f6092b9e1fb63747f2402f516b7ff0f815be4ece4625f2a2ec954f", size = 9262 },
    { url = "https://files.pythonhosted.org/packages/c7/09/05c9cd16cf2374c38c6dbc3b43e84de5fa7435e557985f4403ac7dea33fd/pyobjc_framework_automaticassessmentconfiguration-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:378d233879bb011ed9d0bcf1b0e3c048fb756023d0f6819e997f62acc2c32bc3", size = 9397 },
]

[[package]]
name = "pyobjc-framework-automator"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/63/9f/097ed9f4de9e9491a1b08bb7d85d35a95d726c9e9f5f5bf203b359a436b6/pyobjc_framework_automator-11.1.tar.gz", hash = "sha256:9b46c55a4f9ae2b3c39ff560f42ced66bdd18c093188f0b5fc4060ad911838e4", size = 201439 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/0e/1e/3ed1df2168e596151da2329258951dae334e194d7de3b117c7e29a768ffc/pyobjc_framework_automator-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:af5941f8d90167244209b352512b7779e5590d17dc1e703e087a6cfe79ee3d64", size = 10029 },
    { url = "https://files.pythonhosted.org/packages/25/ed/a92cea530aac0cf08287321ec8123e8447f93461521f46bb329058b322eb/pyobjc_framework_automator-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:3458f836671ea922ad0771f617c927e9c52841c0a6e71b4a5a9dbb438736c207", size = 10040 },
    { url = "https://files.pythonhosted.org/packages/e9/30/c284723dd871e59756d24ddb4a9728db87b9e1b1610d22f3f60ad9de8b45/pyobjc_framework_automator-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:203b888152a78b39a8c67be663ff78a749ebff208ce993b4419fc4409faa1fda", size = 10186 },
    { url = "https://files.pythonhosted.org/packages/89/ac/a1e4e318bb972c2e62bdd215490bc4c24cdfac881e3ade5660d2b1412779/pyobjc_framework_automator-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:651760236cb2d2481faa5afb66da97054850d34fdbebc5e4ee2f83a683a8be10", size = 10086 },
    { url = "https://files.pythonhosted.org/packages/7b/9c/ffcc59f5ff3aadfba6b94ba641c668bca10e0612f8754c25753f0a12f41a/pyobjc_framework_automator-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:112815d2e1b6002b4f9bc644bdae6b02257d249145c79346d7b8bb11e6f76b03", size = 10239 },
]

[[package]]
name = "pyobjc-framework-avfoundation"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-coreaudio" },
    { name = "pyobjc-framework-coremedia" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://files.pythonhosted.org/packages/3c/1f/90cdbce1d3b4861cbb17c12adf57daeec32477eb1df8d3f9ab8551bdadfb/pyobjc_framework_avfoundation-11.1.tar.gz", hash = "sha256:6663056cc6ca49af8de6d36a7fff498f51e1a9a7f1bde7afba718a8ceaaa7377", size = 832178 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/43/30/d5d03dd4a508bdaa2156ff379e9e109020de23cbb6316c5865d341aa6db1/pyobjc_framework_avfoundation-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:94f065db4e87b1baebb5cf9f464cf9d82c5f903fff192001ebc974d9e3132c7e", size = 70746 },
    { url = "https://files.pythonhosted.org/packages/3f/8c/b8ced7700b0e931dc37d14b05e2bead28d2598c887832b3d697da55b1845/pyobjc_framework_avfoundation-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:e204d155a09c186601490e4402dcffb2845a5831079e389b47bd6a341fe5ee63", size = 70773 },
    { url = "https://files.pythonhosted.org/packages/d6/4c/086f4713793aaabdb5134debbf1fdc6c7d4ef5a32a6b35529e2e69580ec8/pyobjc_framework_avfoundation-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:dd3965aad0b236b8ac12f216d688c1a22b963f63e7e4fdb7107dd6790e80ee12", size = 71352 },
    { url = "https://files.pythonhosted.org/packages/a6/5f/d5c4b9812e22c6fdf234421f131efae7c3137e838bb9df9be8bb45cde97b/pyobjc_framework_avfoundation-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:1ab2108b652496b13b9758c295f0f6de53b6d12125cf574ddae84ce28044bce1", size = 71208 },
    { url = "https://files.pythonhosted.org/packages/29/d0/dec23e1745a81f5576cba577fa7218d665f36250a8507eaaa83a84579abf/pyobjc_framework_avfoundation-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:5dd6ac6a57f86b7ed5ac0a965ce54328f6ce77816b4a1fbf0d85c06fb251867a", size = 71680 },
]

[[package]]
name = "pyobjc-framework-avkit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://files.pythonhosted.org/packages/61/ff/9f41f2b8de786871184b48c4e5052cb7c9fcc204e7fee06687fa32b08bed/pyobjc_framework_avkit-11.1.tar.gz", hash = "sha256:d948204a7b94e0e878b19a909f9b33342e19d9ea519571d66a21fce8f72e3263", size = 46825 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/b2/2f/6ec6a4ec7eb9ca329f36bbd2a51750fe5064d44dd437d8615abb7121ec93/pyobjc_framework_avkit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:ef9cd9fe37c6199bfde7ee5cd6e76ede23a6797932882785c53ef3070e209afb", size = 11539 },
    { url = "https://files.pythonhosted.org/packages/16/c8/6f0131f62f70e201a605b762cc05804b01fd493a7f21824d714140b7fd99/pyobjc_framework_avkit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:c5810b349745078ef8b4a562e85afe40de3245127f633d8cabe98aeca765c7fc", size = 11551 },
    { url = "https://files.pythonhosted.org/packages/a9/e6/a5bfa072393416c940a35b182457fee4779cf2f010c5772a9b690522afef/pyobjc_framework_avkit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:023b1cdb78c3aa5873d8abe69697396872b47278208991ec5e5aea4464309b01", size = 11749 },
    { url = "https://files.pythonhosted.org/packages/35/15/fdb3c2dbce6cc7236bced3874fe5cf4b32b3af786447aae033bb1831f5e9/pyobjc_framework_avkit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:a6b418603fc270a8e63c2a5efffa753704fd14bf8bca0657901c49a7cc9b22b5", size = 11587 },
    { url = "https://files.pythonhosted.org/packages/fc/2e/a311d27ac6785bfe51e6276ad326be90ca928cb07d73fc4fb8e8857f7ce0/pyobjc_framework_avkit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:3a5f22bc4f4b0b82c8039d37996882bf4a38f509963d1afa3275a45ddd4a0b00", size = 11766 },
]

[[package]]
name = "pyobjc-framework-avrouting"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/cf/42/94bc18b968a4ee8b6427257f907ffbfc97f8ba6a6202953da149b649d638/pyobjc_framework_avrouting-11.1.tar.gz", hash = "sha256:7db1291d9f53cc58d34b2a826feb721a85f50ceb5e71952e8762baacd3db3fc0", size = 21069 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/01/17/ce199bc7fb3ba1f7b0474554bd71d1bdd3d5a141e1d9722ff9f46c104e1d/pyobjc_framework_avrouting-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:dc309e175abf3961f933f8b341c0504b17f4717931242ebb121a83256b8b5c13", size = 8212 },
    { url = "https://files.pythonhosted.org/packages/72/39/5c550da37c6d5a18a9b4a7d0fd6f7396ca8fbbee8cfccf82f3298e0f86b3/pyobjc_framework_avrouting-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:f52f9d62a3c8485b5687187ea58d905d7edccac9941c444b4add8129841cd031", size = 8230 },
    { url = "https://files.pythonhosted.org/packages/6b/ee/fec9662a0f7756a3440cd1c31be8c3a2db98d9b88210e46ca76b36e151ca/pyobjc_framework_avrouting-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:6a7b335161d327792f42054acb3ff415f7778e1492582df8e91b8609b4b02244", size = 8383 },
    { url = "https://files.pythonhosted.org/packages/41/34/31b10439741980c9f226623ec9cee9649a8ac34a81efd1ad26f72a7d02da/pyobjc_framework_avrouting-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:120c9d65d4f9047b9921f8dced0b4f26d799156bc08ff7e3974217cd036b1bfc", size = 8269 },
    { url = "https://files.pythonhosted.org/packages/1d/7b/9fed48dcc1b94fa20d5435c352bea2ce431541e43b43fb720dcb43fc3d16/pyobjc_framework_avrouting-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:9aa9b0a7ae7ee5874e7d92bebefca4525d5cf1f0aa1f50e78e558984a39cad2e", size = 8410 },
]

[[package]]
name = "pyobjc-framework-backgroundassets"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/08/76/21e1632a212f997d7a5f26d53eb997951978916858039b79f43ebe3d10b2/pyobjc_framework_backgroundassets-11.1.tar.gz", hash = "sha256:2e14b50539d96d5fca70c49f21b69fdbad81a22549e3630f5e4f20d5c0204fc2", size = 24803 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/ad/77/a6ad2df35fd71b3c26f52698d25174899ba1be134766022f5bf804ebf12d/pyobjc_framework_backgroundassets-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:13bf451c59b409b6ce1ac0e717a970a1b03bca7a944a7f19219da0d46ab7c561", size = 9707 },
    { url = "https://files.pythonhosted.org/packages/1d/7f/ed035866ab6c0573c445a9ed1ceb0912119866c130df7684a2332642520e/pyobjc_framework_backgroundassets-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:708466d847a479e1798f31c59fbc5307473d03fa1083f40cfcaa18fd31819c40", size = 9722 },
    { url = "https://files.pythonhosted.org/packages/05/e9/15f540b4bee160fd4b66f294ee4cd326aaa94632bcbee12d4b2448bb74ee/pyobjc_framework_backgroundassets-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:2484a2f9c87e8cae2fc375a39d68ea7ff02e4fb786e4afe88237c51fd5e78ec9", size = 9899 },
    { url = "https://files.pythonhosted.org/packages/9b/aa/17dd9b9def7d9d29c1ee14e1b3100e0bf9dbc5fdd4a12d1bd4c6e79b46d2/pyobjc_framework_backgroundassets-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:a72536ed18cf2462085bbb2184d0a3eecf9b97669c0ef4db45418555a609b534", size = 9774 },
    { url = "https://files.pythonhosted.org/packages/5a/de/852cb10bb11a0e88d2422f24c2bdb8eeeabf9c0a400e1cba03a7af351dca/pyobjc_framework_backgroundassets-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:a4db45048d1021900be5b03136b927773820bcbb40d623aeac54712e1c86d6f6", size = 9948 },
]

[[package]]
name = "pyobjc-framework-browserenginekit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-coreaudio" },
    { name = "pyobjc-framework-coremedia" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://files.pythonhosted.org/packages/30/75/087270d9f81e913b57c7db58eaff8691fa0574b11faf9302340b3b8320f1/pyobjc_framework_browserenginekit-11.1.tar.gz", hash = "sha256:918440cefb10480024f645169de3733e30ede65e41267fa12c7b90c264a0a479", size = 31944 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/89/90/a50bb66a5e041ace99b6c8b1df43b38d5f2e1bf771f57409e4aebf1dfae5/pyobjc_framework_browserenginekit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:9b815b167533015d62832b956e9cfb962bd2026f5a4ccd66718cf3bb2e15ab27", size = 11115 },
    { url = "https://files.pythonhosted.org/packages/44/0a/3cbfc8ca58ed9aeef7498f318ad209164903e64eba1ea94a661a59ee67e6/pyobjc_framework_browserenginekit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:dfe469f8eb1313ea0cbe0616cd3bbc56f62bdd8a683c959819ef01d7e9ac0de7", size = 11134 },
    { url = "https://files.pythonhosted.org/packages/4d/d6/013d10fc2ad2c7095e1b61b1b3db2c38aec403784f81b70237d11ba615a8/pyobjc_framework_browserenginekit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:f3332ffa9ae74cc6633fd17f6d998ac77b8939abbe9ecf95ae56df200ee93853", size = 11322 },
    { url = "https://files.pythonhosted.org/packages/63/ba/59869b4f500a1f7edf6eb84b6e018df37655b0b6b96fc6e2d00dfa3b648d/pyobjc_framework_browserenginekit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:c3195c4fb3b84150fac6dd18ce318eaae17f246f98678825397ed80d6da3c371", size = 11170 },
    { url = "https://files.pythonhosted.org/packages/c2/9a/0e75c06c0f48c368b7eb2d5aa6bde780106fad080fd74a76e109eef6afc6/pyobjc_framework_browserenginekit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:1f4cce594a94d0bc0a020122153f8149c16578fa4761b0e27d868c013f76214c", size = 11369 },
]

[[package]]
name = "pyobjc-framework-businesschat"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/85/be/9d9d9d9383c411a58323ea510d768443287ca21610af652b815b3205ea80/pyobjc_framework_businesschat-11.1.tar.gz", hash = "sha256:69589d2f0cb4e7892e5ecc6aed79b1abd1ec55c099a7faacae6a326bc921259d", size = 12698 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/87/a4/5b8bb268b263678c0908cdaa8bed2534a6caac5862d05236f6c361d130ba/pyobjc_framework_businesschat-11.1-py2.py3-none-any.whl", hash = "sha256:7fdc1219b988ce3ae896bffd01f547c06cec3b4e4b2d0aa04d251444d7f1c2db", size = 3458 },
]

[[package]]
name = "pyobjc-framework-calendarstore"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/41/df/7ca8ee65b16d5fc862d7e8664289472eed918cf4d76921de6bdaa1461c65/pyobjc_framework_calendarstore-11.1.tar.gz", hash = "sha256:858ee00e6a380d9c086c2d7db82c116a6c406234038e0ec8fc2ad02e385dc437", size = 68215 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/c7/94/69cb863bd88349df0f6cf491fd3ca4d674816c4d66270f9e2620cc6e16ed/pyobjc_framework_calendarstore-11.1-py2.py3-none-any.whl", hash = "sha256:bf066e17392c978becf17a61863eb81727bf593a2bfdab261177126072557e24", size = 5265 },
]

[[package]]
name = "pyobjc-framework-callkit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/51/d5/4f0b62ab35be619e8c8d96538a03cf56fde6fd53540e1837e0fa588b3f6c/pyobjc_framework_callkit-11.1.tar.gz", hash = "sha256:b84d5ea38dff0cbe0754f5f9f6f33c742e216f12e7166179a8ec2cf4b0bfca94", size = 46648 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/18/2a/209572a6dba6768a57667e1f87a83ce8cadf18de5d6b1a91b95ce548d0f8/pyobjc_framework_callkit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:554e09ca3dab44d93a89927d9e300f004d2ef0db020b10425a4622b432e7b684", size = 11269 },
    { url = "https://files.pythonhosted.org/packages/8f/74/b0a22adb7ebcd0b81c24ed6e49d3df3b84f73192b667ebd90cb1b6eba917/pyobjc_framework_callkit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:fc5e638ddbc9dd3e9993205d2b077f5db41b6cd4e97b9c5592b7249575f23f04", size = 11284 },
    { url = "https://files.pythonhosted.org/packages/a2/98/3f65e4853a4a45b0cf369e5bbb0d9efaad93589461d155119feb88e8ff7b/pyobjc_framework_callkit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:bc1d2349dab93f7a0d298b01893828d7f46aded9122a341469b835d977a0646d", size = 11494 },
    { url = "https://files.pythonhosted.org/packages/e4/95/d89e97351570fcfaae843dea29aa06c2a3ff00a6ea8ea4c3e68478620afa/pyobjc_framework_callkit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:b69b4262897f2701348ea0da36afe32d60f84e2a036baf13e258a97875b25a6c", size = 11305 },
    { url = "https://files.pythonhosted.org/packages/2f/38/939b73759cfd1bf6367290c31bfe576fafdd7a351aa867c7c29eba962d1e/pyobjc_framework_callkit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:8266ee797fdabb657f7cb4fa808404fc33fcf3f31d4bcab1ab3c53d272e1ff83", size = 11504 },
]

[[package]]
name = "pyobjc-framework-carbon"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/39/a4/d751851865d9a78405cfec0c8b2931b1e96b9914e9788cd441fa4e8290d0/pyobjc_framework_carbon-11.1.tar.gz", hash = "sha256:047f098535479efa3ab89da1ebdf3cf9ec0b439a33a4f32806193886e9fcea71", size = 37291 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/84/44/f1a20b5aa3833af4d461074c479263a410ef90d17dbec11f78ad9c34dbab/pyobjc_framework_carbon-11.1-py2.py3-none-any.whl", hash = "sha256:1bf66853e939315ad7ee968170b16dd12cb838c42b80dfcd5354687760998825", size = 4753 },
]

[[package]]
name = "pyobjc-framework-cfnetwork"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/6f/49/7b24172e3d6eb0ddffc33a7498a2bea264aa2958c3fecaeb463bef88f0b8/pyobjc_framework_cfnetwork-11.1.tar.gz", hash = "sha256:ad600163eeadb7bf71abc51a9b6f2b5462a018d3f9bb1510c5ce3fdf2f22959d", size = 79069 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/c2/31/05b4fb79e7f738f7f7d7a58734de2fab47d9a1fb219c2180e8c07efe2550/pyobjc_framework_cfnetwork-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:70beb8095df76e0e8eb7ab218be1e69ae180e01a4d77f7cad73c97b4eb7a296a", size = 19141 },
    { url = "https://files.pythonhosted.org/packages/2d/b1/5ea76ffd6413be8c65ec02e4552e3da3ee2bd37449e0854e3c8c559e7e42/pyobjc_framework_cfnetwork-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:5dd866fcbe6870931373636d19144544344f0f89685f6720e4a45453957702dd", size = 19148 },
    { url = "https://files.pythonhosted.org/packages/ba/df/b4897033b0368e4b6c4e5f643c593801677b2590d48dcb93d1c5a1d66c0f/pyobjc_framework_cfnetwork-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:62ccc6dcaaa5877534d21f93a15861a3d8af95888123d659f9ff5383d1a2a1f4", size = 19406 },
    { url = "https://files.pythonhosted.org/packages/25/9b/f277fb7a7da804a2b53b2f3dacf1f0196e63536580023bd5377344e1407a/pyobjc_framework_cfnetwork-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:4b998daa3e6ce253c48455365f004647b3b1da2f313fbc8a5a607e460b4d5567", size = 19186 },
    { url = "https://files.pythonhosted.org/packages/e2/f6/80b5c7bb8247c2bb17c3869389a591f480ef771073c4642fbe49e65f1614/pyobjc_framework_cfnetwork-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:2e9a4ce6b416bff881df499d9060c1096220ef8c20e519108a7b91692d1fd1d7", size = 19407 },
]

[[package]]
name = "pyobjc-framework-cinematic"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-avfoundation" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-coremedia" },
    { name = "pyobjc-framework-metal" },
]
sdist = { url = "https://files.pythonhosted.org/packages/57/6f/c2d0b49e01e654496a1781bafb9da72a6fbd00f5abb39dc4a3a0045167c7/pyobjc_framework_cinematic-11.1.tar.gz", hash = "sha256:efde39a6a2379e1738dbc5434b2470cd187cf3114ffb81390b3b1abda470b382", size = 25522 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/05/bd/a9b51c770bd96546a101c9e9994f851b87336f168a77048241517ca4db8c/pyobjc_framework_cinematic-11.1-py2.py3-none-any.whl", hash = "sha256:b62c024c1a9c7890481bc2fdfaf0cd3c251a4a08357d57dc1795d98920fcdbd1", size = 4562 },
]

[[package]]
name = "pyobjc-framework-classkit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/7a/8b/5150b4faddd15d5dd795bc62b2256c4f7dafc983cfa694fcf88121ea0016/pyobjc_framework_classkit-11.1.tar.gz", hash = "sha256:ee1e26395eb00b3ed5442e3234cdbfe925d2413185af38eca0477d7166651df4", size = 39831 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/75/79/2552fd5e1da73dffb35589469b3cd8c0928e3100462761350d19ea922e59/pyobjc_framework_classkit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:161dcb9b718649e6331a5eab5a76c2b43a9b322b15b37b3f8f9c5faad12ee6d1", size = 8911 },
    { url = "https://files.pythonhosted.org/packages/59/1c/a06623c3d78949c9d5eae7c7e753e6c8c75e2ae7a0b8ccae40a1b6180e0a/pyobjc_framework_classkit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:08000deb43004d16fb39ccd83b3de30e1e3b72639a79d05206d7d5c15f005b3a", size = 8928 },
    { url = "https://files.pythonhosted.org/packages/b3/c3/e0a966134c8022f1d922b27fea6a50ec1118c12fdfa65b2ce4efaa7c84d6/pyobjc_framework_classkit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:ef28d042964b0f757569e72df737bb049b531c33b7d06a705ce2dcfa4e6e45d8", size = 9082 },
    { url = "https://files.pythonhosted.org/packages/c7/66/d5113269ee84bebc03576c53394e2b59c25da01f932f2e1cdfc5bd05a5a1/pyobjc_framework_classkit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:be279d91f10d68ad9a256e96d26d8975e35b9b1bb304c82491766d29ad252b0d", size = 8958 },
    { url = "https://files.pythonhosted.org/packages/ad/72/fff0a96bd7fd9a83ee074330070ebe4a53d99a3c0620c786bb59c04c4a7c/pyobjc_framework_classkit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:9a1b9d31f9b23e05b92769bbdb4ef2167a59b3b24aefa6af86448f5087a2e105", size = 9120 },
]

[[package]]
name = "pyobjc-framework-cloudkit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-accounts" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-coredata" },
    { name = "pyobjc-framework-corelocation" },
]
sdist = { url = "https://files.pythonhosted.org/packages/58/a6/bfe5be55ed95704efca0e86b218155a9c801735107cedba3af8ea4580a05/pyobjc_framework_cloudkit-11.1.tar.gz", hash = "sha256:40d2dc4bf28c5be9b836b01e4d267a15d847d756c2a65530e1fcd79b2825e86d", size = 122778 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/25/d9/5570a217cef8130708e860b86f4f22bb5827247c97121523a9dfd4784148/pyobjc_framework_cloudkit-11.1-py2.py3-none-any.whl", hash = "sha256:c583e40c710cf85ebe34173d1d2995e832a20127edc8899b2f35b13f98498af1", size = 10870 },
]

[[package]]
name = "pyobjc-framework-cocoa"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
]
sdist = { url = "https://files.pythonhosted.org/packages/4b/c5/7a866d24bc026f79239b74d05e2cf3088b03263da66d53d1b4cf5207f5ae/pyobjc_framework_cocoa-11.1.tar.gz", hash = "sha256:87df76b9b73e7ca699a828ff112564b59251bb9bbe72e610e670a4dc9940d038", size = 5565335 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/68/da/41c0f7edc92ead461cced7e67813e27fa17da3c5da428afdb4086c69d7ba/pyobjc_framework_cocoa-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:806de56f06dfba8f301a244cce289d54877c36b4b19818e3b53150eb7c2424d0", size = 388983 },
    { url = "https://files.pythonhosted.org/packages/4e/0b/a01477cde2a040f97e226f3e15e5ffd1268fcb6d1d664885a95ba592eca9/pyobjc_framework_cocoa-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:54e93e1d9b0fc41c032582a6f0834befe1d418d73893968f3f450281b11603da", size = 389049 },
    { url = "https://files.pythonhosted.org/packages/bc/e6/64cf2661f6ab7c124d0486ec6d1d01a9bb2838a0d2a46006457d8c5e6845/pyobjc_framework_cocoa-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:fd5245ee1997d93e78b72703be1289d75d88ff6490af94462b564892e9266350", size = 393110 },
    { url = "https://files.pythonhosted.org/packages/33/87/01e35c5a3c5bbdc93d5925366421e10835fcd7b23347b6c267df1b16d0b3/pyobjc_framework_cocoa-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:aede53a1afc5433e1e7d66568cc52acceeb171b0a6005407a42e8e82580b4fc0", size = 392644 },
    { url = "https://files.pythonhosted.org/packages/c1/7c/54afe9ffee547c41e1161691e72067a37ed27466ac71c089bfdcd07ca70d/pyobjc_framework_cocoa-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:1b5de4e1757bb65689d6dc1f8d8717de9ec8587eb0c4831c134f13aba29f9b71", size = 396742 },
]

[[package]]
name = "pyobjc-framework-collaboration"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/66/49/9dbe8407d5dd663747267c1234d1b914bab66e1878d22f57926261a3063b/pyobjc_framework_collaboration-11.1.tar.gz", hash = "sha256:4564e3931bfc51773623d4f57f2431b58a39b75cb964ae5c48d27ee4dde2f4ea", size = 16839 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/62/24/4c9deedcc62d223a45d4b4fa16162729923d2b3e2231467de6ecd079f3f8/pyobjc_framework_collaboration-11.1-py2.py3-none-any.whl", hash = "sha256:3629ea5b56c513fb330d43952afabb2df2a2ac2f9048b8ec6e8ab4486191390a", size = 4891 },
]

[[package]]
name = "pyobjc-framework-colorsync"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/b5/97/7613b6041f62c52f972e42dd5d79476b56b84d017a8b5e4add4d9cfaca36/pyobjc_framework_colorsync-11.1.tar.gz", hash = "sha256:7a346f71f34b2ccd1b020a34c219b85bf8b6f6e05283d503185aeb7767a269dd", size = 38999 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/30/d5/c8fc7c47cbb9865058094dc9cf3f57879156ff55fb261cf199e7081d1db7/pyobjc_framework_colorsync-11.1-py2.py3-none-any.whl", hash = "sha256:d19d6da2c7175a3896a63c9b40a8ab98ade0779a5b40062789681501c33efd5c", size = 5971 },
]

[[package]]
name = "pyobjc-framework-contacts"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/a6/85/34868b6447d552adf8674bac226b55c2baacacee0d67ee031e33805d6faa/pyobjc_framework_contacts-11.1.tar.gz", hash = "sha256:752036e7d8952a4122296d7772f274170a5f35a53ee6454a27f3e1d9603222cc", size = 84814 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/30/c8/0d47af11112bf382e059cfe2dd03be98914f0621ddff8858bb9af864f8c5/pyobjc_framework_contacts-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:576ee4aec05d755444bff10b45833f73083b5b3d1b2740e133b92111f7765e54", size = 12141 },
    { url = "https://files.pythonhosted.org/packages/11/af/375aa44e9e00aa66e373c4c3893a0db341d93f90e2d62a277287dc553841/pyobjc_framework_contacts-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:09b873d2bd739fea63d744430defb04ce4b44af064aaf0b6bf558eea23f82bd7", size = 12160 },
    { url = "https://files.pythonhosted.org/packages/a0/b9/effeda0eefedced16d4a002ab0c0a331be506d5bc7ff290788ac8eb0b2a9/pyobjc_framework_contacts-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:23312bb4bfc5aafecdac84ca402189e312e754e9dc0586d8f282d225c3952c00", size = 12319 },
    { url = "https://files.pythonhosted.org/packages/93/9c/25c6e7ba0fe1d18206decd3e2b47bf110047dda89f7411fe430c0bfd4268/pyobjc_framework_contacts-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:3409aba6e23cb179b3fe932c1a0a53d7b273ac8292d5adf1bf6849e925cc0955", size = 12237 },
    { url = "https://files.pythonhosted.org/packages/32/fc/0a519a38eada4bf4ed6f502920077e5313fdb1f3eec668438460a797ce47/pyobjc_framework_contacts-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:910f40a2e4d80a97f282bfdecba0f5ff95201b11844acd3f9cb9522db364ab57", size = 12393 },
]

[[package]]
name = "pyobjc-framework-contactsui"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-contacts" },
]
sdist = { url = "https://files.pythonhosted.org/packages/3f/57/8765b54a30edaa2a56df62e11e7c32e41b6ea300513256adffa191689368/pyobjc_framework_contactsui-11.1.tar.gz", hash = "sha256:5bc29ea2b10a342018e1b96be6b140c10ebe3cfb6417278770feef5e88026a1f", size = 20031 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/46/b6/50ec09f1bb18c422b8c079e02328689f32e977b43ab7651c05e8274854dc/pyobjc_framework_contactsui-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:c34a6f27ef5aa4742cc44fd5b4d16fe1e1745ff839578b4c059faf2c58eee3ca", size = 7875 },
    { url = "https://files.pythonhosted.org/packages/8b/3f/72170303c11945c360b83fa1c0d3f91638dc5de1ef9f9a2b880252378430/pyobjc_framework_contactsui-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:f3b4f0225645a26ed9e6c008c2e8c217035b4a50fa9cd6623c628a11c37924d0", size = 7886 },
    { url = "https://files.pythonhosted.org/packages/ad/d7/fd11ac75bd6eb5d23225f7d1ac910c2b47481caff6e04b883bec04c28de2/pyobjc_framework_contactsui-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:666586174b306b33b791d2edee021cd979a8c970d444f906ed294e27583a6b54", size = 8044 },
    { url = "https://files.pythonhosted.org/packages/05/64/aee816b82564c693fea199178ac791dd384d602b6c772b7f829fb1b8405d/pyobjc_framework_contactsui-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:7901eed3c669ad52cca86089c443fd30820b21586bf758e03fb83696f435ba87", size = 7937 },
    { url = "https://files.pythonhosted.org/packages/34/d4/fe2495ac19d83cc211a639b3654d4ea0f173d053cca387a4448a70d1a1f6/pyobjc_framework_contactsui-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:8b03bd175095b4774c55bd5f38a01942e945b668bea15b9dc3b4f1a28b1a8696", size = 8091 },
]

[[package]]
name = "pyobjc-framework-coreaudio"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/39/c0/4ab6005cf97e534725b0c14b110d4864b367c282b1c5b0d8f42aad74a83f/pyobjc_framework_coreaudio-11.1.tar.gz", hash = "sha256:b7b89540ae7efc6c1e3208ac838ef2acfc4d2c506dd629d91f6b3b3120e55c1b", size = 141032 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/3d/fe/c43521642db98a4ec29fa535781c1316342bb52d5fc709696cbb1e8ca6cd/pyobjc_framework_coreaudio-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:2538d1242dab4e27efb346eafbad50594e7e95597fa7220f0bab2099c825da55", size = 36765 },
    { url = "https://files.pythonhosted.org/packages/82/9b/24d03ace273585de2d04385f06b895ce92caf8f5af430b060618ebce9dbe/pyobjc_framework_coreaudio-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:f73d996df1e721931d9f78050e1708735a173dbe3a76d9c71fb36e04f7208478", size = 36779 },
    { url = "https://files.pythonhosted.org/packages/91/23/aa78365e45d0d04fc37e21cf7d69dc0d11e17b564e83cb5bcd98e89cdf45/pyobjc_framework_coreaudio-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:67dae111b78d91c26c753dbfbccc3ea5498cfda3dfe83c6f3778628b435e1e7b", size = 38480 },
    { url = "https://files.pythonhosted.org/packages/3e/58/fc6d752a68f28567fa6d6d6a229122c829e2251f79ec7304fe0572e0fdcd/pyobjc_framework_coreaudio-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:9527a16a2b88b37bace578d499f21229f9a33b9afdcdd35d4f44374cb8eb9ab6", size = 36910 },
    { url = "https://files.pythonhosted.org/packages/9e/4c/c1c5624418dea005d9965ba690d3649afc33371ade213841ab51922af751/pyobjc_framework_coreaudio-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:6ba8b67f185c0e3f26b17ae525cee3f411bc8d6e9c9a8bfd899a28f594623d2f", size = 38567 },
]

[[package]]
name = "pyobjc-framework-coreaudiokit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-coreaudio" },
]
sdist = { url = "https://files.pythonhosted.org/packages/f1/4e/c49b26c60047c511727efe994b412276c487dfe90f1ee0fced0bddbdf8a3/pyobjc_framework_coreaudiokit-11.1.tar.gz", hash = "sha256:0b461c3d6123fda4da6b6aaa022efc918c1de2e126a5cf07d2189d63fa54ba40", size = 21955 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/18/27/d8ff6293851a7d9665724fa5c324d28200776ec10a04b850ba21ad1f9be1/pyobjc_framework_coreaudiokit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:20440a2926b1d91da8efc8bc060e77c7a195cb0443dbf3770eaca9e597276748", size = 7266 },
    { url = "https://files.pythonhosted.org/packages/13/e6/89aa525271d19f0ea11799021f364181dd62dbfe77ecb4fc0a7d4e579cd2/pyobjc_framework_coreaudiokit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:11d42770dfbc6a8af8d5fa39a4f700f0067d7e6c7ba9335e6624d89de3c599a9", size = 7273 },
    { url = "https://files.pythonhosted.org/packages/a5/70/f9b13b7822a53bed794525214ccca63b018901c113ebfd45e2159447f3cf/pyobjc_framework_coreaudiokit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:6fea7c7ea5305e8cbd75808ec4edcde8e2320137f227b3d771266dd9a71e1fa5", size = 7429 },
    { url = "https://files.pythonhosted.org/packages/a7/d0/aba10b553783c9940b81cb67ad3cae4d4c72e67d4c1af8f4cbe2d9a642d8/pyobjc_framework_coreaudiokit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:a71447196a48869b551a2e3b6ba92f39241cb64d0257120505c62ddb611aef0f", size = 7301 },
    { url = "https://files.pythonhosted.org/packages/90/9a/a4b7fc47896f1739b8346d21c1b40f536e317f3de416b5cbf12c50445979/pyobjc_framework_coreaudiokit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:8d012561eb95877f0214aa0cd13043b1a2693add4a9534d1e6fb82f6d7183c7c", size = 7451 },
]

[[package]]
name = "pyobjc-framework-corebluetooth"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/3d/fe/2081dfd9413b7b4d719935c33762fbed9cce9dc06430f322d1e2c9dbcd91/pyobjc_framework_corebluetooth-11.1.tar.gz", hash = "sha256:1deba46e3fcaf5e1c314f4bbafb77d9fe49ec248c493ad00d8aff2df212d6190", size = 60337 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/8a/bc/083ea1ae57a31645df7fad59921528f6690995f7b7c84a203399ded7e7fe/pyobjc_framework_corebluetooth-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:36bef95a822c68b72f505cf909913affd61a15b56eeaeafea7302d35a82f4f05", size = 13163 },
    { url = "https://files.pythonhosted.org/packages/3e/b5/d07cfa229e3fa0cd1cdaa385774c41907941d25b693cf55ad92e8584a3b3/pyobjc_framework_corebluetooth-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:992404b03033ecf637e9174caed70cb22fd1be2a98c16faa699217678e62a5c7", size = 13179 },
    { url = "https://files.pythonhosted.org/packages/7a/10/476bca43002a6d009aed956d5ed3f3867c8d1dcd085dde8989be7020c495/pyobjc_framework_corebluetooth-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:ebb8648f5e33d98446eb1d6c4654ba4fcc15d62bfcb47fa3bbd5596f6ecdb37c", size = 13358 },
    { url = "https://files.pythonhosted.org/packages/b0/49/6c050dffb9acc49129da54718c545bc5062f61a389ebaa4727bc3ef0b5a9/pyobjc_framework_corebluetooth-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:e84cbf52006a93d937b90421ada0bc4a146d6d348eb40ae10d5bd2256cc92206", size = 13245 },
    { url = "https://files.pythonhosted.org/packages/36/15/9068e8cb108e19e8e86cbf50026bb4c509d85a5d55e2d4c36e292be94337/pyobjc_framework_corebluetooth-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:4da1106265d7efd3f726bacdf13ba9528cc380fb534b5af38b22a397e6908291", size = 13439 },
]

[[package]]
name = "pyobjc-framework-coredata"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/00/e3/af497da7a7c895b6ff529d709d855a783f34afcc4b87ab57a1a2afb3f876/pyobjc_framework_coredata-11.1.tar.gz", hash = "sha256:fe9fd985f8e06c70c0fb1e6bbea5b731461f9e76f8f8d8e89c7c72667cdc6adf", size = 260628 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/5b/ac/77935aa9891bd6be952b1e6780df2bae748971dd0fe0b5155894004840bd/pyobjc_framework_coredata-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:c9b2374784e67694a18fc8c120a12f11b355a20b643c01f23ae2ce87330a75e0", size = 16443 },
    { url = "https://files.pythonhosted.org/packages/75/50/17631c3f172d9681faad210b035fa3d2c01f59468b574dbc088512853cc2/pyobjc_framework_coredata-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:007160eb10bb8c789076f231e3d625d8875ca42eb5a806fdab5d0277c48866f8", size = 16457 },
    { url = "https://files.pythonhosted.org/packages/1f/d7/c736d0a945efe806996335324a241f9e2726ebc8a91c9c3cfaa2d788c63b/pyobjc_framework_coredata-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:699ad568f98f58e88e642159c91ffff0c68ce3d1ec798e4af8333b27431fd058", size = 16608 },
    { url = "https://files.pythonhosted.org/packages/fa/b9/22c554e3a7d121145aedaab580a88bf35935fc81f693e5071ed8aa7d299e/pyobjc_framework_coredata-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:d84afaccbb4f18dbda4c557cd059b7adc2116436a065353e25e7cbc840d9f8b4", size = 16500 },
    { url = "https://files.pythonhosted.org/packages/d1/2e/8562252a30644ac5209365358a30cfc53a46609959beaafceffde7381e54/pyobjc_framework_coredata-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:89dde863eff01ed6b5f8d88c764a08b154ef37078397c98c5f403e8798723b9d", size = 16659 },
]

[[package]]
name = "pyobjc-framework-corehaptics"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/5f/83/cc997ec4687a68214dd3ad1bdf64353305f5c7e827fad211adac4c28b39f/pyobjc_framework_corehaptics-11.1.tar.gz", hash = "sha256:e5da3a97ed6aca9b7268c8c5196c0a339773a50baa72d1502d3435dc1a2a80f1", size = 42722 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/21/d0/0fb20c0f19beae53c905653ffdcbf32e3b4119420c737ff4733f7ebb3b29/pyobjc_framework_corehaptics-11.1-py2.py3-none-any.whl", hash = "sha256:8f8c47ccca5052d07f95d2f35e6e399c5ac1f2072ba9d9eaae902edf4e3a7af4", size = 5363 },
]

[[package]]
name = "pyobjc-framework-corelocation"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/95/ef/fbd2e01ec137208af7bfefe222773748d27f16f845b0efa950d65e2bd719/pyobjc_framework_corelocation-11.1.tar.gz", hash = "sha256:46a67b99925ee3d53914331759c6ee110b31bb790b74b05915acfca41074c206", size = 104508 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/95/cb/282d59421cdb89a5e5fcce72fc37d6eeace98a2a86d71f3be3cd47801298/pyobjc_framework_corelocation-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:562e31124f80207becfd8df01868f73fa5aa70169cc4460e1209fb16916e4fb4", size = 12752 },
    { url = "https://files.pythonhosted.org/packages/de/cb/c4672fcfa5e998cfd0dd165717ec312f7e6cbac06ecb4a0e227dbc4d7e27/pyobjc_framework_corelocation-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:0f8182835429118a55ed65963c80f5b2892d190747b986e8395b1cd99f41a1d0", size = 12768 },
    { url = "https://files.pythonhosted.org/packages/47/e7/ef83b4d6fca57bd09a56064fdcb55792b7497279b1dac3de781c86ed40ec/pyobjc_framework_corelocation-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:bc3f27802415aa62330a2d2507adc3a9b98a89d6de7d1033ebe6b8c461610831", size = 12910 },
    { url = "https://files.pythonhosted.org/packages/a3/9f/9a107d223babd3d846873bd30897d4411585523403adfaec91963abcb281/pyobjc_framework_corelocation-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:17ce2530bd5a0dca9059eb11bc647d920490bcdd35b5cac1e160f51f0297bdc8", size = 12800 },
    { url = "https://files.pythonhosted.org/packages/0d/54/3a841006c2bf0fa4797c2fb77c79150b526800d191a539a8f2d0e54a377e/pyobjc_framework_corelocation-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:a384d9fcba2c041d8f8115b51a07ef11c391bc30f72560aaea8b94db6b3b225c", size = 12953 },
]

[[package]]
name = "pyobjc-framework-coremedia"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/95/5d/81513acd219df77a89176f1574d936b81ad6f6002225cabb64d55efb7e8d/pyobjc_framework_coremedia-11.1.tar.gz", hash = "sha256:82cdc087f61e21b761e677ea618a575d4c0dbe00e98230bf9cea540cff931db3", size = 216389 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/2c/d1/b3d004d6a2d2188d196779d92fe8cfa2533f5722cd216fbc4f0cffc63b24/pyobjc_framework_coremedia-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:ea5055298af91e463ffa7977d573530f9bada57b8f2968dcc76a75e339b9a598", size = 29015 },
    { url = "https://files.pythonhosted.org/packages/1c/23/cafd29011d14eac27fc55770157ebb8e02ffed9f75e01f24e97616417c4c/pyobjc_framework_coremedia-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:7ecdb64c743ffe9fd3949c7cc9109891b9f399a0852717fcb969d33c4e7ba527", size = 29031 },
    { url = "https://files.pythonhosted.org/packages/de/a6/ca85b7d9d000e8e2748bcacde356278cb90f6ca9aed54dce6a42d1716ba8/pyobjc_framework_coremedia-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:969ce357c616f6835f47e27d1e73964374cdb671476571dfd358894a8ced06f2", size = 29094 },
    { url = "https://files.pythonhosted.org/packages/b8/3d/56d530cf504a6eef84f51c8f6f845af8b947f6108e41db5e0b5189d5a667/pyobjc_framework_coremedia-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:bf1da05c297776c297ab3489ebf18d954efdff530acbdd6e70c32be811e20ec6", size = 29043 },
    { url = "https://files.pythonhosted.org/packages/a4/bc/b237ecd4954a0f07450469236ca45412edb7d8715ff7fc175ac519e7c472/pyobjc_framework_coremedia-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:aa942d9ad0cf5bc4d3ede8779c3fac2f04cf3857687f2fb8505bae3378d04b95", size = 29111 },
]

[[package]]
name = "pyobjc-framework-coremediaio"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/64/68/9cef2aefba8e69916049ff43120e8794df8051bdf1f690a55994bbe4eb57/pyobjc_framework_coremediaio-11.1.tar.gz", hash = "sha256:bccd69712578b177144ded398f4695d71a765ef61204da51a21f0c90b4ad4c64", size = 108326 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/4b/b5/5dd941c1d7020a78b563a213fb8be7c6c3c1073c488914e158cd5417f4f7/pyobjc_framework_coremediaio-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:39ad2518de9943c474e5ca0037e78f92423c3352deeee6c513a489016dac1266", size = 17250 },
    { url = "https://files.pythonhosted.org/packages/08/44/cd98e1dacdd28c4e80fe1b0dde3a5171494735cb4a7b8b5775825b824b96/pyobjc_framework_coremediaio-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:9e0a079fe790ce8a69d11bea46b315c9a0d3f3999a2f09e2ef4fcc4430a47c42", size = 17226 },
    { url = "https://files.pythonhosted.org/packages/f9/66/89a3c01d1d1a0e7b510ade14a2c604883d6846d8279095ff4849f9989f9c/pyobjc_framework_coremediaio-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:5a94f9e507b470ce7dcb887e79ccf19e98693a606ad34462d711004e3edd88c3", size = 17564 },
    { url = "https://files.pythonhosted.org/packages/2b/70/4a137a8a8b618ad025586ebe7f459989ead666e41825053d297c1a104f72/pyobjc_framework_coremediaio-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:0a7ffded00a7dc6f0bf4a44a6832f0150d45a83886486148b71ccc67c70ef215", size = 17257 },
    { url = "https://files.pythonhosted.org/packages/1b/d7/054313e96c40efe8f535ef1a172cc612c53a55f27eb5e2805a84727155d6/pyobjc_framework_coremediaio-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:5ff161025ef28d5e2eed90db0e8b828cb361281b799b16b1885711ca0addc1aa", size = 17572 },
]

[[package]]
name = "pyobjc-framework-coremidi"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/06/ca/2ae5149966ccd78290444f88fa62022e2b96ed2fddd47e71d9fd249a9f82/pyobjc_framework_coremidi-11.1.tar.gz", hash = "sha256:095030c59d50c23aa53608777102bc88744ff8b10dfb57afe24b428dcd12e376", size = 107817 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/c2/2d/57c279dd74a9073d1416b10b05ebb9598f4868cad010d87f46ef4b517324/pyobjc_framework_coremidi-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:deb9120478a831a898f22f68737fc683bb9b937e77556e78b75986aebd349c55", size = 24277 },
    { url = "https://files.pythonhosted.org/packages/1e/66/dfdc7a5dc5a44b1660015bb24454ca0cbdf436e631e39917c495475dbb24/pyobjc_framework_coremidi-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:c2e1ab122501206ceae07123fdc433e91a5f1a97224f80ece0717b6f36ad2029", size = 24308 },
    { url = "https://files.pythonhosted.org/packages/46/fe/200f286d5506efdc6c6d150eda24909a89f5c856a7a5003db0a423f66943/pyobjc_framework_coremidi-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:3462a158214adb7ebc785fb6924e674c58dcd471888dbca5e2e77381f3f1bbdc", size = 24463 },
    { url = "https://files.pythonhosted.org/packages/7e/a5/053ad95a662544ef036c18d45680a4016b9eb897fb7dfcbcef13602b947a/pyobjc_framework_coremidi-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:f4b70864cae295f27b5d51817c0768fade7c1335a59410910146e5f2a54c475c", size = 24320 },
    { url = "https://files.pythonhosted.org/packages/7d/2c/e97e4f8ea07ffca82daa0ed0159f6d5ca03699b2a1944f4c4adb4d64bd21/pyobjc_framework_coremidi-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:2ef1a10f6230fce82b931670470158404657d9fb9ac558a77b46b547e9978524", size = 24474 },
]

[[package]]
name = "pyobjc-framework-coreml"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/0d/5d/4309f220981d769b1a2f0dcb2c5c104490d31389a8ebea67e5595ce1cb74/pyobjc_framework_coreml-11.1.tar.gz", hash = "sha256:775923eefb9eac2e389c0821b10564372de8057cea89f1ea1cdaf04996c970a7", size = 82005 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/3e/9e/a1b6d30b4f91c7cc4780e745e1e73a322bd3524a773f66f5eac0b1600d85/pyobjc_framework_coreml-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:c768b03d72488b964d753392e9c587684961d8237b69cca848b3a5a00aea79c9", size = 11436 },
    { url = "https://files.pythonhosted.org/packages/95/95/f8739958ccf7cbaaf172653b3665cfcee406c5503a49828130b618b93d3f/pyobjc_framework_coreml-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:10d51f8a5fe8d30c7ec70304a2324df76b48b9fbef30ee0f0c33b99a49ae8853", size = 11452 },
    { url = "https://files.pythonhosted.org/packages/57/d1/881cef8f09f022ba6534d98f0bb1c3ad5e68dbdda91173d88fa1524c0526/pyobjc_framework_coreml-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:4df25ee233430f016ffcb4e88506b54c8e7b668c93197e6a1341761530a5922c", size = 11682 },
    { url = "https://files.pythonhosted.org/packages/cf/92/81be40d2b4a9a52e75ff0051dfd9258cf5aad529d86144f0730d1f7ec034/pyobjc_framework_coreml-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:287a2a059016d02d8c40e0d29e70226142a4969db97ad79cefc70ec9bf0ab29e", size = 11551 },
    { url = "https://files.pythonhosted.org/packages/b7/08/bb686f0ede51d1e09be395f176613ee4834f47ce081c13e4ee464d14c748/pyobjc_framework_coreml-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:a479c3d759aff3695f72c7915a78df6e92e0eca7027abaa8b4a07e876ba1dbfb", size = 11729 },
]

[[package]]
name = "pyobjc-framework-coremotion"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/a5/95/e469dc7100ea6b9c29a074a4f713d78b32a78d7ec5498c25c83a56744fc2/pyobjc_framework_coremotion-11.1.tar.gz", hash = "sha256:5884a568521c0836fac39d46683a4dea3d259a23837920897042ffb922d9ac3e", size = 67050 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/e9/17/ffa3cf9fde9df31f3d6ecb38507c61c6d8d81276d0a9116979cafd5a0ab7/pyobjc_framework_coremotion-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:8c3b33228a170bf8495508a8923451ec600435c7bff93d7614f19c913baeafd1", size = 10368 },
    { url = "https://files.pythonhosted.org/packages/7c/2b/ade312f6bda6c368112bc2151834e664c22ae7d6d1f2ce33347b84ece7fb/pyobjc_framework_coremotion-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:ac5302deaab99a7443cad63f125061a90040852d4f8efb58492542a612b2afe3", size = 10393 },
    { url = "https://files.pythonhosted.org/packages/63/51/380d1b2b072b379a4740b725bdec4119c0c82bc66c55a2a62ca2fa0ec478/pyobjc_framework_coremotion-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:d67413a56989154dab7bf1b69c14b0b2387d87d3a4c8e3c8a9fc0230f061e8ab", size = 10534 },
    { url = "https://files.pythonhosted.org/packages/03/4f/efbab9157e74d39074a3ce05e0494174203cbdb28a48c59fb2464b0fffed/pyobjc_framework_coremotion-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:42fb307b86999d078503ff79bdf8df4d1c27d38763db6b1c5c0f4054241f67a3", size = 10443 },
    { url = "https://files.pythonhosted.org/packages/78/90/1da8d8acbcd8fe348bd2e94a26e5f289e621af1d42f86c57b4d3de940650/pyobjc_framework_coremotion-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:708431c53f483bc6da199375227ffea1b4e8e7d8c81d162492db3fc36893fb53", size = 10606 },
]

[[package]]
name = "pyobjc-framework-coreservices"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-fsevents" },
]
sdist = { url = "https://files.pythonhosted.org/packages/a8/a9/141d18019a25776f507992f9e7ffc051ca5a734848d8ea8d848f7c938efc/pyobjc_framework_coreservices-11.1.tar.gz", hash = "sha256:cf8eb5e272c60a96d025313eca26ff2487dcd02c47034cc9db39f6852d077873", size = 1245086 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/fa/0f/52827197a1fa1dabefd77803920eaf340f25e0c81944844ab329d511cade/pyobjc_framework_coreservices-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:6bd313ec326efd715b4b10c3ebcc9f054e3ee3178be407b97ea225cd871351d2", size = 30252 },
    { url = "https://files.pythonhosted.org/packages/9d/dc/8a0414dd81054062a56a54db5c1cbb35c715081c9210ed69d5fed8046ebe/pyobjc_framework_coreservices-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:8aee505dca56afc5363d8d0dff0b2d26583a8d0f3ac37674cef86f66c51a2934", size = 30271 },
    { url = "https://files.pythonhosted.org/packages/44/e3/494bbc589b0a02ad7ab657fdf67359298b007112b65a2f4416d61176a4c4/pyobjc_framework_coreservices-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:4ffa188322ab9d05c6964926959dedba5cc04534232f1eff03aee5f09faa499e", size = 30282 },
    { url = "https://files.pythonhosted.org/packages/ab/0b/1c666c01c003e1b73baa5c71cab5a50000b1180e5c1cbf14b02f20cf8c3b/pyobjc_framework_coreservices-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:21e9e86192d719cd5c899cc0e931110733da0b5bbbf606681e5fccd4dd39c174", size = 30294 },
    { url = "https://files.pythonhosted.org/packages/ff/39/6026aaeef8b0eb0c25089374132a9bdbeffbc10f93cab589162efd43dc86/pyobjc_framework_coreservices-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:74dcc295245f07754328bada9577b189e3abef71607d013e939751c1b5b55729", size = 30309 },
]

[[package]]
name = "pyobjc-framework-corespotlight"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/31/c7/b67ebfb63b7ccbfda780d583056d1fd4b610ba3839c8ebe3435b86122c61/pyobjc_framework_corespotlight-11.1.tar.gz", hash = "sha256:4dd363c8d3ff7619659b63dd31400f135b03e32435b5d151459ecdacea14e0f2", size = 87161 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/b9/f8/06b7edfeabe5b3874485b6e5bbe4a39d9f2e1f44348faa7cb320fbc6f21a/pyobjc_framework_corespotlight-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:7cedd3792fe1fe2a8dc65a8ff1f70baf12415a5dc9dc4d88f987059567d7e694", size = 9977 },
    { url = "https://files.pythonhosted.org/packages/7d/ce/812ae5a7f97a57abce1b2232280d5838a77d5454e5b05d79c3e654ad7400/pyobjc_framework_corespotlight-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:546d0d9b101de4ca20449f3807d1f88e5c26de0345a8bfefc70f12f87efb8433", size = 9997 },
    { url = "https://files.pythonhosted.org/packages/5c/ee/9c432c1735f537c5b56dae43f6d2f2dd4922cac45c8e072e5a405b3ab81b/pyobjc_framework_corespotlight-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:f562cc65865066f8e2e5d96c868fd7f463d8280f1ef01df85250fc1150feed0e", size = 10137 },
    { url = "https://files.pythonhosted.org/packages/c1/b8/3a8910e0ffbec9f13f090be0e7cd40ad8144069dcdb80062f13c4768be5c/pyobjc_framework_corespotlight-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:bce3d84f97014228b244c734aea3ec03b257573b22c097dff4eb176a80cd29a9", size = 10043 },
    { url = "https://files.pythonhosted.org/packages/b5/7e/36e3342da3f5d05979729570c1630e442305118d5cb6462e81d21feb74e7/pyobjc_framework_corespotlight-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:f59d0d2f0411db102d16490e47b457b994c613f1b980869fa3a151863da7aa4c", size = 10188 },
]

[[package]]
name = "pyobjc-framework-coretext"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://files.pythonhosted.org/packages/65/e9/d3231c4f87d07b8525401fd6ad3c56607c9e512c5490f0a7a6abb13acab6/pyobjc_framework_coretext-11.1.tar.gz", hash = "sha256:a29bbd5d85c77f46a8ee81d381b847244c88a3a5a96ac22f509027ceceaffaf6", size = 274702 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/32/67/9cc5189c366e67dc3e5b5976fac73cc6405841095f795d3fa0d5fc43d76a/pyobjc_framework_coretext-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:1597bf7234270ee1b9963bf112e9061050d5fb8e1384b3f50c11bde2fe2b1570", size = 30175 },
    { url = "https://files.pythonhosted.org/packages/b0/d1/6ec2ef4f8133177203a742d5db4db90bbb3ae100aec8d17f667208da84c9/pyobjc_framework_coretext-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:37e051e8f12a0f47a81b8efc8c902156eb5bc3d8123c43e5bd4cebd24c222228", size = 30180 },
    { url = "https://files.pythonhosted.org/packages/0a/84/d4a95e49f6af59503ba257fbed0471b6932f0afe8b3725c018dd3ba40150/pyobjc_framework_coretext-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:56a3a02202e0d50be3c43e781c00f9f1859ab9b73a8342ff56260b908e911e37", size = 30768 },
    { url = "https://files.pythonhosted.org/packages/64/4c/16e1504e06a5cb23eec6276835ddddb087637beba66cf84b5c587eba99be/pyobjc_framework_coretext-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:15650ba99692d00953e91e53118c11636056a22c90d472020f7ba31500577bf5", size = 30155 },
    { url = "https://files.pythonhosted.org/packages/ad/a4/cbfa9c874b2770fb1ba5c38c42b0e12a8b5aa177a5a86d0ad49b935aa626/pyobjc_framework_coretext-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:fb27f66a56660c31bb956191d64b85b95bac99cfb833f6e99622ca0ac4b3ba12", size = 30768 },
]

[[package]]
name = "pyobjc-framework-corewlan"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/c6/d8/03aff3c75485fc999e260946ef1e9adf17640a6e08d7bf603d31cfcf73fc/pyobjc_framework_corewlan-11.1.tar.gz", hash = "sha256:4a8afea75393cc0a6fe696e136233aa0ed54266f35a47b55a3583f4cb078e6ce", size = 65792 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/09/8a/74feabaad1225eb2c44d043924ed8caea31683e6760cd9b918b8d965efea/pyobjc_framework_corewlan-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:7bd0775d2466ad500aad4747d8a889993db3a14240239f30ef53c087745e9c8e", size = 10016 },
    { url = "https://files.pythonhosted.org/packages/ef/12/792146e163aa4504bc7870c77c4ec2425f9a05fa615a2b5c9cbec89b0fc6/pyobjc_framework_corewlan-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:3c66643a97fcf3aa797fda997a3afc28d8d9bba9727dd5c0e68a313899d780f7", size = 10026 },
    { url = "https://files.pythonhosted.org/packages/d8/e8/e0bf4c66192e85fb92a3ae01b50e34f2283568f7a0e5548f52db81b8b146/pyobjc_framework_corewlan-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:6dc28264b56b18096c8869cce3f85e519fd27936f19524bb77458572ccfd7518", size = 10178 },
    { url = "https://files.pythonhosted.org/packages/8e/c1/c860300f585de3f57b9f6c30c554e10708d57ec5ac1e920214b496638c0c/pyobjc_framework_corewlan-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:872de75409a710bb9a461e64e97185f8489d01898ec1b02c3e058c04606b61cf", size = 10051 },
    { url = "https://files.pythonhosted.org/packages/ff/76/5bdb6b672d7b59a477cfcb35d7c0166a4bd86e7bc571ff693d62fccb75b2/pyobjc_framework_corewlan-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:14c7af9135ba0a920192af4dc50219bbf6185fcbb5de7041f097e1a1c8509587", size = 10210 },
]

[[package]]
name = "pyobjc-framework-cryptotokenkit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/eb/92/7fab6fcc6bb659d6946cfb2f670058180bcc4ca1626878b0f7c95107abf0/pyobjc_framework_cryptotokenkit-11.1.tar.gz", hash = "sha256:5f82f44d9ab466c715a7c8ad4d5ec47c68aacd78bd67b5466a7b8215a2265328", size = 59223 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/76/f1/4cb9c90a55ec13301d60ac1c4d774c37b4ebc6db6331d3853021c933fcc8/pyobjc_framework_cryptotokenkit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:6384cb1d86fc586e2da934a5a37900825bd789e3a5df97517691de9af354af0c", size = 12543 },
    { url = "https://files.pythonhosted.org/packages/c6/c8/b64a56ed65719b1dfb9c06da0772d4a76eceb830672aab237df745bc31f7/pyobjc_framework_cryptotokenkit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:a55c0e57ab164aa5ce562e4d9e69026339067ecb4888638995690f1c43b79cfa", size = 12559 },
    { url = "https://files.pythonhosted.org/packages/9a/32/bb53ae388a99927fee626ba2746d3a6ec388cbc14b8f4ce91a35dd6b55e2/pyobjc_framework_cryptotokenkit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:cb3e1bd344e794cb98343171b5501a1a3b75548ef5385bda3d5ec613c0b98045", size = 12742 },
    { url = "https://files.pythonhosted.org/packages/4a/34/9f30580ccddff6b6555603af920ef61a420ba515eb8ab7e10fbd9c1464a5/pyobjc_framework_cryptotokenkit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:faab9493e36095c0257598e25ef81c50bcdb3afb5843a82e6dfad8c7d1f47bcf", size = 12531 },
    { url = "https://files.pythonhosted.org/packages/4e/07/baec88c0cfe9cd327753ce527dfab3b622bb5e2b45d3ff5bb8f4d2dae40c/pyobjc_framework_cryptotokenkit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:efd89e5b024475701f6e9bec4cf1c2563e1bab37e79288397e09d9ad4e53d174", size = 12734 },
]

[[package]]
name = "pyobjc-framework-datadetection"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/7d/4d/65c61d8878b44689e28d5729be9edbb73e20b1b0500d1095172cfd24aea6/pyobjc_framework_datadetection-11.1.tar.gz", hash = "sha256:cbe0080b51e09b2f91eaf2a9babec3dcf2883d7966bc0abd8393ef7abfcfc5db", size = 13485 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/08/c4/ef2136e4e0cc69b02479295822aa33c8e26995b265c8a1184867b65a0a06/pyobjc_framework_datadetection-11.1-py2.py3-none-any.whl", hash = "sha256:5afd3dde7bba3324befb7a3133c9aeaa5088efd72dccc0804267a74799f4a12f", size = 3482 },
]

[[package]]
name = "pyobjc-framework-devicecheck"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/f3/f2/b1d263f8231f815a9eeff15809f4b7428dacdc0a6aa267db5ed907445066/pyobjc_framework_devicecheck-11.1.tar.gz", hash = "sha256:8b05973eb2673571144d81346336e749a21cec90bd7fcaade76ffd3b147a0741", size = 13954 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/39/72/17698a0d68b1067b20b32b4afd74bcafb53a7c73ae8fc608addc7b9e7a37/pyobjc_framework_devicecheck-11.1-py2.py3-none-any.whl", hash = "sha256:8edb36329cdd5d55e2c2c57c379cb5ba1f500f74a08fe8d2612b1a69b7a26435", size = 3668 },
]

[[package]]
name = "pyobjc-framework-devicediscoveryextension"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/9a/b8/102863bfa2f1e414c88bb9f51151a9a58b99c268a841b59d46e0dcc5fe6d/pyobjc_framework_devicediscoveryextension-11.1.tar.gz", hash = "sha256:ae160ea40f25d3ee5e7ce80ac9c1b315f94d0a4c7ccb86920396f71c6bf799a0", size = 14298 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/67/89/fce0c0c89746f399d13e08b40fc12e29a2495f4dcebd30893336d047af18/pyobjc_framework_devicediscoveryextension-11.1-py2.py3-none-any.whl", hash = "sha256:96e5b13c718bd0e6c80fbd4e14b8073cffc88b3ab9bb1bbb4dab7893a62e4f11", size = 4249 },
]

[[package]]
name = "pyobjc-framework-dictionaryservices"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-coreservices" },
]
sdist = { url = "https://files.pythonhosted.org/packages/d6/13/c46f6db61133fee15e3471f33a679da2af10d63fa2b4369e0cd476988721/pyobjc_framework_dictionaryservices-11.1.tar.gz", hash = "sha256:39c24452d0ddd037afeb73a1742614c94535f15b1c024a8a6cc7ff081e1d22e7", size = 10578 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/6c/86/4e757b4064a0feb8d60456672560adad0bb5df530ba6621fe65d175dbd90/pyobjc_framework_dictionaryservices-11.1-py2.py3-none-any.whl", hash = "sha256:92f4871066653f18e2394ac93b0a2ab50588d60020f6b3bd93e97b67cd511326", size = 3913 },
]

[[package]]
name = "pyobjc-framework-discrecording"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/a5/b2/d8d1a28643c2ab681b517647bacb68496c98886336ffbd274f0b2ad28cdc/pyobjc_framework_discrecording-11.1.tar.gz", hash = "sha256:37585458e363b20bb28acdb5cc265dfca934d8a07b7baed2584953c11c927a87", size = 123004 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/5e/17/032fa44bb66b6a20c432f3311072f88478b42dcf39b21ebb6c3bbdf2954f/pyobjc_framework_discrecording-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:e29bc8c3741ae52fae092f892de856dbab2363e71537a8ae6fd026ecb88e2252", size = 14581 },
    { url = "https://files.pythonhosted.org/packages/55/d4/a9e2fa7aa38b4ecca9668b3ae9ae4244bf335974c42b46313c3ec631c73a/pyobjc_framework_discrecording-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:2d18158366d124852ad58291954611ebdcc43263a3bb75d7fd273408e67720e2", size = 14592 },
    { url = "https://files.pythonhosted.org/packages/5e/3c/660d06446b8e67121b755aeb20ba369234845675d25c658127e43fdbc835/pyobjc_framework_discrecording-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:b027eca3a0391196d4335fcbd50c03ef1e8f5ce095411ed51a081328b4945bf5", size = 14763 },
    { url = "https://files.pythonhosted.org/packages/31/bb/a1b694e9649b5148254325b3f78d658bb4919fc8d0d1c20c85313178b3da/pyobjc_framework_discrecording-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:9cb36715bebdbbe1ad95e3c17359c2f5d3f6479a26b527ea1032154ca7cf3e09", size = 14623 },
    { url = "https://files.pythonhosted.org/packages/62/25/e2552e4e8de09d8e8fe53f87cc0878c3cf2ff2030a6352a22d45a0484be8/pyobjc_framework_discrecording-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:7c33421d6bed0993d9f1861dbf38b717b9a9e49dfb98fdf8b3cd8d558fdd50eb", size = 14799 },
]

[[package]]
name = "pyobjc-framework-discrecordingui"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-discrecording" },
]
sdist = { url = "https://files.pythonhosted.org/packages/25/53/d71717f00332b8fc3d8a5c7234fdc270adadfeb5ca9318a55986f5c29c44/pyobjc_framework_discrecordingui-11.1.tar.gz", hash = "sha256:a9f10e2e7ee19582c77f0755ae11a64e3d61c652cbd8a5bf52756f599be24797", size = 19370 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/4a/a6/505af43f7a17e0ca3d45e099900764e8758e0ca65341e894b74ade513556/pyobjc_framework_discrecordingui-11.1-py2.py3-none-any.whl", hash = "sha256:33233b87d7b85ce277a51d27acca0f5b38485cf1d1dc8e28a065910047766ee2", size = 4721 },
]

[[package]]
name = "pyobjc-framework-diskarbitration"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/da/2a/68fa0c99e04ec1ec24b0b7d6f5b7ec735d5e8a73277c5c0671438a69a403/pyobjc_framework_diskarbitration-11.1.tar.gz", hash = "sha256:a933efc6624779a393fafe0313e43378bcae2b85d6d15cff95ac30048c1ef490", size = 19866 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/1f/72/9534ca88effbf2897e07b722920b3f10890dbc780c6fff1ab4893ec1af10/pyobjc_framework_diskarbitration-11.1-py2.py3-none-any.whl", hash = "sha256:6a8e551e54df481a9081abba6fd680f6633babe5c7735f649731b22896bb6f08", size = 4849 },
]

[[package]]
name = "pyobjc-framework-dvdplayback"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/b8/76/77046325b1957f0cbcdf4f96667496d042ed4758f3413f1d21df5b085939/pyobjc_framework_dvdplayback-11.1.tar.gz", hash = "sha256:b44c36a62c8479e649133216e22941859407cca5796b5f778815ef9340a838f4", size = 64558 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/59/0c/f0fefa171b6938010d87194e26e63eea5c990c33d2d7828de66802f57c36/pyobjc_framework_dvdplayback-11.1-py2.py3-none-any.whl", hash = "sha256:6094e4651ea29540ac817294b27e1596b9d1883d30e78fb5f9619daf94ed30cb", size = 8221 },
]

[[package]]
name = "pyobjc-framework-eventkit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/b4/c4/cbba8f2dce13b9be37ecfd423ba2b92aa3f209dbb58ede6c4ce3b242feee/pyobjc_framework_eventkit-11.1.tar.gz", hash = "sha256:5643150f584243681099c5e9435efa833a913e93fe9ca81f62007e287349b561", size = 75177 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/05/0a/384b9ff4c6380cac310cb7b92c145896c20a690192dbfc07b38909787ded/pyobjc_framework_eventkit-11.1-py2.py3-none-any.whl", hash = "sha256:c303207610d9c742f4090799f60103cede466002f3c89cf66011c8bf1987750b", size = 6805 },
]

[[package]]
name = "pyobjc-framework-exceptionhandling"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/19/0d/c72a885b40d28a99b586447f9ea6f400589f13d554fcd6f13a2c841bb6d2/pyobjc_framework_exceptionhandling-11.1.tar.gz", hash = "sha256:e010f56bf60ab4e9e3225954ebb53e9d7135d37097043ac6dd2a3f35770d4efa", size = 17890 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/7f/81/dde9c73bf307b62c2d605fc818d3e49f857f39e0841766093dbc9ea47b08/pyobjc_framework_exceptionhandling-11.1-py2.py3-none-any.whl", hash = "sha256:31e6538160dfd7526ac0549bc0fce5d039932aea84c36abbe7b49c79ffc62437", size = 7078 },
]

[[package]]
name = "pyobjc-framework-executionpolicy"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/0b/cf/54431846508c5d5bb114a415ebb96187da5847105918169e42f4ca3b00e6/pyobjc_framework_executionpolicy-11.1.tar.gz", hash = "sha256:3280ad2f4c5eaf45901f310cee0c52db940c0c63e959ad082efb8df41055d986", size = 13496 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/a6/d2/cb192d55786d0f881f2fb60d45b61862a1fcade945f6a7a549ed62f47e61/pyobjc_framework_executionpolicy-11.1-py2.py3-none-any.whl", hash = "sha256:7d4141e572cb916e73bb34bb74f6f976a8aa0a396a0bffd1cf66e5505f7c76c8", size = 3719 },
]

[[package]]
name = "pyobjc-framework-extensionkit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/ce/7d/89adf16c7de4246477714dce8fcffae4242778aecd0c5f0ad9904725f42c/pyobjc_framework_extensionkit-11.1.tar.gz", hash = "sha256:c114a96f13f586dbbab8b6219a92fa4829896a645c8cd15652a6215bc8ff5409", size = 19766 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/90/2a/93105b5452d2ff680a47e38a3ec6f2a37164babd95e0ab976c07984366de/pyobjc_framework_extensionkit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:d505a64617c9db4373eb386664d62a82ba9ffc909bffad42cb4da8ca8e244c66", size = 7914 },
    { url = "https://files.pythonhosted.org/packages/b8/67/1dbd000d9d0c17d838c471dbb48229fca1ca18fad8453c19ecc01d3312a1/pyobjc_framework_extensionkit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:abbadbea5b18e4a6944c3c428753ee298a133cbf601c70e9586b14e3aebf649b", size = 7927 },
    { url = "https://files.pythonhosted.org/packages/fb/35/e5d1e633ad5b0c5163afd19ac0b02740e47a45de78d6f2599de3bc6542a5/pyobjc_framework_extensionkit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:5c2e203cb8134be1dd7df73d74c630adbaaf43d78eba04be451ea4f8bf582e22", size = 8069 },
    { url = "https://files.pythonhosted.org/packages/9f/18/4c5ad3cbbf4f984f5316c2264789080d3caeaae47293cc739a59814f682f/pyobjc_framework_extensionkit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:3507f67dd06285c09bbdf5216a1148f5dd3a2f10eee7a9318dd14430bf6e67ee", size = 7974 },
    { url = "https://files.pythonhosted.org/packages/75/1b/84ac20bb341a739681ad46ea0ec3d83b40f4716fa6ed966ad93274abe423/pyobjc_framework_extensionkit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:2767635e57b277e051719fa53c7683396ebdbcf3d40d44c1296758978ca8c92a", size = 8122 },
]

[[package]]
name = "pyobjc-framework-externalaccessory"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/d9/a3/519242e6822e1ddc9e64e21f717529079dbc28a353474420da8315d0a8b1/pyobjc_framework_externalaccessory-11.1.tar.gz", hash = "sha256:50887e948b78a1d94646422c243ac2a9e40761675e38b9184487870a31e83371", size = 23123 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/7d/1b/e2def12aca9162b0fe0bbf0790d35595d46b2ef12603749c42af9234ffca/pyobjc_framework_externalaccessory-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:00caf75b959db5d14118d78c04085e2148255498839cdee735a0b9f6ef86b6a2", size = 8903 },
    { url = "https://files.pythonhosted.org/packages/b4/6f/1340c193c30ade7b0394b2c8f29f3e6dd501eb23a416a728cc9a23efaec2/pyobjc_framework_externalaccessory-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:50b796a4721db87863a28cd55668cb1547fcc28834afda2032e500cdab5b3d95", size = 8915 },
    { url = "https://files.pythonhosted.org/packages/ec/27/1617435d3827a544c2ed2660ecd2e317c82cc8e819a55daa491973349e58/pyobjc_framework_externalaccessory-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:569124b686569c48e3855fff128f438a2b46af06280eac2a516aaa214ad325de", size = 9080 },
    { url = "https://files.pythonhosted.org/packages/5b/cf/b825117308f1dcd82c7484d5ee7e3c9a2a00cd39b5bc2a73e43fd9803ceb/pyobjc_framework_externalaccessory-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:318772e698c6363e8c3c81229d93b639f5066a02a742ba1ab10cfdef3101d88b", size = 8961 },
    { url = "https://files.pythonhosted.org/packages/a2/25/2b9aefc07e06df08501fbd3f3dc1da555e0943e9e169b842b6ac52505907/pyobjc_framework_externalaccessory-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:d259724665617fc4f3e666d353b756a67cabb74e6f9d7b8f6f250a2d4bf05cb7", size = 9135 },
]

[[package]]
name = "pyobjc-framework-fileprovider"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/1b/80/3ebba2c1e5e3aeae989fe038c259a93e7e7e18fd56666ece514d000d38ea/pyobjc_framework_fileprovider-11.1.tar.gz", hash = "sha256:748ca1c75f84afdf5419346a24bf8eec44dca071986f31f00071dc191b3e9ca8", size = 91696 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/49/b2/859d733b0110e56511478ba837fd8a7ba43aa8f8c7e5231b9e3f0258bfbf/pyobjc_framework_fileprovider-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:ce6092dfe74c78c0b2abc03bfc18a0f5d8ddc624fc6a1d8dfef26d7796653072", size = 19622 },
    { url = "https://files.pythonhosted.org/packages/91/ed/ae5ce4a18752ea2da5d7238f7847119af8c7dc69ffd9fb1369414c9745d2/pyobjc_framework_fileprovider-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:9af41255df395a40a6e0b08c4410be5463f3ea91d8c9be61f6bd114252490ab2", size = 19627 },
    { url = "https://files.pythonhosted.org/packages/84/83/530daae946318689d29457da995577996de5965ff41b4b3b8b604617ff46/pyobjc_framework_fileprovider-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:d2720acdd582756ebda34418981e7646b7b85588b0b8fdafba7016eb657be6b8", size = 19859 },
    { url = "https://files.pythonhosted.org/packages/e2/de/8411450fc602f841c7001651fc71487de6fc4d418beb5b83a576c734b0e5/pyobjc_framework_fileprovider-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:0e48015bf50b3e56312c640ec6efde73cf3855e29b6d70d173a88957d9d74d27", size = 19970 },
    { url = "https://files.pythonhosted.org/packages/d9/51/65d9be84e8c33c0341ed79392e9b9896a1f3ca21d96271d293389a94f264/pyobjc_framework_fileprovider-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:95ed3a03741076a4479aabb616b1e3ea022025a0ad842147a1200c27709019e2", size = 20211 },
]

[[package]]
name = "pyobjc-framework-fileproviderui"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-fileprovider" },
]
sdist = { url = "https://files.pythonhosted.org/packages/75/ed/0f5af06869661822c4a70aacd674da5d1e6b6661240e2883bbc7142aa525/pyobjc_framework_fileproviderui-11.1.tar.gz", hash = "sha256:162a23e67f59e1bb247e84dda88d513d7944d815144901a46be6fe051b6c7970", size = 13163 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/62/01/667e139a0610494e181fccdce519f644166f3d8955b330674deba5876f0d/pyobjc_framework_fileproviderui-11.1-py2.py3-none-any.whl", hash = "sha256:f2765f114c2f4356aa41fb45c621fa8f0a4fae0b6d3c6b1a274366f5fe7fe829", size = 3696 },
]

[[package]]
name = "pyobjc-framework-findersync"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/2a/82/c6b670494ac0c4cf14cf2db0dfbe0df71925d20595404939383ddbcc56d3/pyobjc_framework_findersync-11.1.tar.gz", hash = "sha256:692364937f418f0e4e4abd395a09a7d4a0cdd55fd4e0184de85ee59642defb6e", size = 15045 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/61/10/748ff914c5b7fbae5fa2436cd44b11caeabb8d2f6f6f1b9ab581f70f32af/pyobjc_framework_findersync-11.1-py2.py3-none-any.whl", hash = "sha256:c72b0fd8b746b99cfa498da36c5bb333121b2080ad73fa8cbea05cd47db1fa82", size = 4873 },
]

[[package]]
name = "pyobjc-framework-fsevents"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/8e/83/ec0b9ba355dbc34f27ed748df9df4eb6dbfdd9bbd614b0f193752f36f419/pyobjc_framework_fsevents-11.1.tar.gz", hash = "sha256:d29157d04124503c4dfa9dcbbdc8c34d3bab134d3db3a48d96d93f26bd94c14d", size = 29587 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/13/c7/378d78e0fd956370f2b120b209117384b5b98925c6d8210a33fd73db4a15/pyobjc_framework_fsevents-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:8b51d120b8f12a1ca94e28cf74113bf2bfd4c5aee7035b452e895518f4df7630", size = 13147 },
    { url = "https://files.pythonhosted.org/packages/18/dc/3b7e75b9f8284257740679509b54f61da2a114cf805d7d3523053e4c6c19/pyobjc_framework_fsevents-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:fad5ada269f137afabd622b5fc04884c668ae1c7914a8791bab73b1d972f7713", size = 13164 },
    { url = "https://files.pythonhosted.org/packages/dd/53/07d62a8642bfddee43cd96301abeed97e858757d363423cf6e383d91f900/pyobjc_framework_fsevents-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:ff064cfa9d9cffb5d4ab476fb5091604568744d961c670aced037b2b6f0d0185", size = 13525 },
    { url = "https://files.pythonhosted.org/packages/54/1c/529de91b3ec8f8efc4bb3067678b3071f255637b17168e1d6f0132a8d729/pyobjc_framework_fsevents-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:9191ee2819f1d5dcae1559e4a66f19be03da3a103bccdc417e6888bcb5659f8f", size = 13047 },
    { url = "https://files.pythonhosted.org/packages/67/21/f4e72a3761510abe93c089aa77b1f01bc1018ff47df1d09f430de9e1aea5/pyobjc_framework_fsevents-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:3289192f4d60e5b26f8ac88ae4049a11eff47caa6fb76ce34e3f7df405119905", size = 13501 },
]

[[package]]
name = "pyobjc-framework-fskit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/46/47/d1f04c6115fa78936399a389cc5e0e443f8341c9a6c1c0df7f6fdbe51286/pyobjc_framework_fskit-11.1.tar.gz", hash = "sha256:9ded1eab19b4183cb04381e554bbbe679c1213fd58599d6fc6e135e93b51136f", size = 42091 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/59/8f/db8f03688db77bfa4b78e89af1d89e910c5e877e94d58bdb3e93cc302e5d/pyobjc_framework_fskit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:1e50b8f949f1386fada73b408463c87eb81ef7fd0b3482bacf0c206a73723013", size = 19948 },
    { url = "https://files.pythonhosted.org/packages/7a/31/0dd6ad9dfce080d6e567326fe7243261740ef1090f72409322040f55a426/pyobjc_framework_fskit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:cc2390934a23b6407aa7802b11978374301444c3135835ad3373f7b4930c24eb", size = 19959 },
    { url = "https://files.pythonhosted.org/packages/96/ba/8655c5959e28fc8b1806a0e0c0b6a47b615de586990efc8ff82a344177a3/pyobjc_framework_fskit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:44fe7b6781c8fd0552b13ab3d0ec21176cd7cd685a8a61d712f9e4e42eb2f736", size = 20201 },
    { url = "https://files.pythonhosted.org/packages/18/ab/f576e3b078a3afe7930f6dbf8614d91ab08c3574bef970079c679c09c2e0/pyobjc_framework_fskit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:1d3793938e6d9b871483d4a6fad8f93d554bcbebd1fe7bed20e3f5d2feaa814b", size = 20166 },
    { url = "https://files.pythonhosted.org/packages/6d/b2/42f72c4e6b0d61a393e66ea921c451bdfdfd6043cf24ae509018b336dbfb/pyobjc_framework_fskit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:e38f9c449647109e5b14dc4a17f425efca10c7e539a3836ebdd1f9c0ef725a3b", size = 20437 },
]

[[package]]
name = "pyobjc-framework-gamecenter"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/1b/8e/b594fd1dc32a59462fc68ad502be2bd87c70e6359b4e879a99bcc4beaf5b/pyobjc_framework_gamecenter-11.1.tar.gz", hash = "sha256:a1c4ed54e11a6e4efba6f2a21ace92bcf186e3fe5c74a385b31f6b1a515ec20c", size = 31981 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/99/52/0e56f21a6660a4f43882ec641b9e19b7ea92dc7474cec48cda1c9bed9c49/pyobjc_framework_gamecenter-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:779cdf8f52348be7f64d16e3ea37fd621d5ee933c032db3a22a8ccad46d69c59", size = 18634 },
    { url = "https://files.pythonhosted.org/packages/3e/fc/64a1e9dc4874a75ceed6e70bb07d5e2a3460283c7737e639a0408ec1b365/pyobjc_framework_gamecenter-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:6ff8905a5a7bfd86cb2b95671b452be0836f79db065b8d8b3bb2a1a5750ffd0d", size = 18638 },
    { url = "https://files.pythonhosted.org/packages/d5/0b/5a8559056ee1cd2fea7405d3843de900b410a14134c33eb112b9fa42201d/pyobjc_framework_gamecenter-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:a73ca7027b2b827e26075b46551fe42425d4a68985022baa4413329a3a2c16ff", size = 18920 },
    { url = "https://files.pythonhosted.org/packages/65/3a/b704f516ef405cb8911afd826fe775af6e06e22ce72bdd0e6c692e303b25/pyobjc_framework_gamecenter-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:2a2cb6471d4d4b19f124c7e91a32882a0fab6e326bb0415915fd8f3b91cfc311", size = 18808 },
    { url = "https://files.pythonhosted.org/packages/b4/c9/4759a330d40d10810b5ebf06286d44088e7c0ef5e4e5523d32045cc93495/pyobjc_framework_gamecenter-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:90132bb32f5ed6607e13c6f39346ad621611cb92cea308ced661a6ba1305b94e", size = 19093 },
]

[[package]]
name = "pyobjc-framework-gamecontroller"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/70/4c/1dd62103092a182f2ab8904c8a8e3922d2b0a80a7adab0c20e5fd0207d75/pyobjc_framework_gamecontroller-11.1.tar.gz", hash = "sha256:4d5346faf90e1ebe5602c0c480afbf528a35a7a1ad05f9b49991fdd2a97f105b", size = 115783 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/40/e3/e35bccb0284046ef716db4897b70d061b8b16c91fb2c434b1e782322ef56/pyobjc_framework_gamecontroller-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:d2cbc0c6c7d9c63e6b5b0b124d0c2bad01bb4b136f3cbc305f27d31f8aab6083", size = 20850 },
    { url = "https://files.pythonhosted.org/packages/ae/eb/42469724725f5d0f11c197aadbb0c5db1647ba69579df4e8d13f553bed1c/pyobjc_framework_gamecontroller-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:4866b25df05f583af06095e7103ddd2fbb2484b0ac2c78fd2cd825f995e524fa", size = 20862 },
    { url = "https://files.pythonhosted.org/packages/c3/43/7430884d24989c07e4e9394c905b02b3aedee7397960dd329a3c44e29c22/pyobjc_framework_gamecontroller-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:98f3f7afcbbe473a53537da42b2cdc0363df2647289eb66e8c762e4b46c23e73", size = 21108 },
    { url = "https://files.pythonhosted.org/packages/69/55/5eb0027bfa985125ca152dd9720aec8e6d580689cc23326bc1a749c68133/pyobjc_framework_gamecontroller-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:de3892b8d09a65a3413d85a2f0762eba092afda8d97cbf9cda0417689cfb7027", size = 21281 },
    { url = "https://files.pythonhosted.org/packages/7f/4f/8c32cf541b972a72e158bcdd1eb95f3180f2eb4532eee9fde8bc58f6961e/pyobjc_framework_gamecontroller-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:afe9f3aed8c900ebe63ee4f6e53c73c2fef7e503f6388afd39f46b31487f84a3", size = 21531 },
]

[[package]]
name = "pyobjc-framework-gamekit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://files.pythonhosted.org/packages/5b/7b/ba141ec0f85ca816f493d1f6fe68c72d01092e5562e53c470a0111d9c34b/pyobjc_framework_gamekit-11.1.tar.gz", hash = "sha256:9b8db075da8866c4ef039a165af227bc29393dc11a617a40671bf6b3975ae269", size = 165397 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/1f/23/094e4fe38f2de029365604f0b7dffde7b0edfc57c3d388294c20ed663de2/pyobjc_framework_gamekit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:f945c7cfe53c4a349a03a1272f2736cc5cf88fe9e7a7a407abb03899635d860c", size = 21952 },
    { url = "https://files.pythonhosted.org/packages/22/2c/9a35fb83a1df7588e2e60488aa425058ee7f01b5a9d4947f74f62a130bf3/pyobjc_framework_gamekit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:8c7f2bf7ecf44ca678cfdf76f23b32d9c2d03006a0af9ad8e60d9114d6be640a", size = 21968 },
    { url = "https://files.pythonhosted.org/packages/7f/23/205eb0532238e79a56bab54820b0e39aedc546429e054dc12d55ca44bb23/pyobjc_framework_gamekit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:a7c8fce8a2c4614e3dd88b002540e67423e3efd41aa26d576db2de0fc61651b9", size = 22246 },
    { url = "https://files.pythonhosted.org/packages/17/49/f297db34e3cdea78b03ec05bcf280b5afcefe7cb3b674705ca5705ee8bf1/pyobjc_framework_gamekit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:555cb8d868fd2699ad70d4f9e7efccaa5df1995893050d05d478cb8f24dbf876", size = 22171 },
    { url = "https://files.pythonhosted.org/packages/85/6e/5c886206d9b34870b66224e1a953afa431dd0c1247d29e5ae0606d06ad33/pyobjc_framework_gamekit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:10331a69282b9554ce7ae618dc9ff68e96451759f6cfc687e188c82ba6b0e2ff", size = 22472 },
]

[[package]]
name = "pyobjc-framework-gameplaykit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-spritekit" },
]
sdist = { url = "https://files.pythonhosted.org/packages/e0/07/f38b1d83eac10ea4f75c605ffc4850585740db89b90842d311e586ee36cd/pyobjc_framework_gameplaykit-11.1.tar.gz", hash = "sha256:9ae2bee69b0cc1afa0e210b4663c7cdbb3cc94be1374808df06f98f992e83639", size = 73399 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/e7/f5/65bdbefb9de7cbc2edf0b1f76286736536e31c216cfac1a5f84ea15f0fc1/pyobjc_framework_gameplaykit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:0e4f34db8177b8b4d89fd22a2a882a6c9f6e50cb438ea2fbbf96845481bcd80d", size = 13091 },
    { url = "https://files.pythonhosted.org/packages/25/4c/011e20a8e9ff1270d3efb6c470c3cd8af10dcd2b05042721b1a777aca7a6/pyobjc_framework_gameplaykit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:78c513bc53bafd996d896f6f4535f2700b4916013417f8b41f47045790c6208d", size = 13109 },
    { url = "https://files.pythonhosted.org/packages/50/a1/31a50e79dfb9983b53220d0a1148a05544062829af76a20febfa2def0b41/pyobjc_framework_gameplaykit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:30e15e4e8df9b1c0ca92bfabf79f6b12a286e544e67762b14dd3023c53e41978", size = 13316 },
    { url = "https://files.pythonhosted.org/packages/8d/8c/240c75848df95c29ce1c8aec1e2ac163f0405bcd6456c55075e438fbc92d/pyobjc_framework_gameplaykit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:4dbea3471b5d4a82b37ddca41bfddd63380c31050de7392e2467fabebcd110b8", size = 13122 },
    { url = "https://files.pythonhosted.org/packages/9c/1a/6590c96f57cda822620e66d8e21b5e55a62b14d040f38b0920f21645109e/pyobjc_framework_gameplaykit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:51abecafc1b55fcc9a5d73c078ea2d5a75964e0facf2c867a25d7f4f40238331", size = 13333 },
]

[[package]]
name = "pyobjc-framework-healthkit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/af/66/fa76f7c8e36e4c10677d42d91a8e220c135c610a06b759571db1abe26a32/pyobjc_framework_healthkit-11.1.tar.gz", hash = "sha256:20f59bd9e1ffafe5893b4eff5867fdfd20bd46c3d03bc4009219d82fc6815f76", size = 202009 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/c7/08/12fca070ad2dc0b9c311df209b9b6d275ee192cb5ccbc94616d9ddd80d88/pyobjc_framework_healthkit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:ab4350f9fe65909107dd7992b367a6c8aac7dc31ed3d5b52eeb2310367d0eb0b", size = 20311 },
    { url = "https://files.pythonhosted.org/packages/5d/26/0337f1b4607a3a13a671a6b07468726943e0d28a462998fcd902f7df6fbf/pyobjc_framework_healthkit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:8b6c739e17362897f0b1ba4aa4dc395b3d0c3855b87423eaeb6a89f910adc43f", size = 20330 },
    { url = "https://files.pythonhosted.org/packages/f4/da/8681afc37504797f747c45be6780f2ef12b9c2a7703cda8f8cf9e48918ca/pyobjc_framework_healthkit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:2d1b76b04e9e33ac9441cafa695766938eac04f8c8c69f7efd93a6aceb6eca40", size = 20502 },
    { url = "https://files.pythonhosted.org/packages/2e/7a/d8e9db3de92e432340d2b7c65dabace75650d426186658606acb5babc7c1/pyobjc_framework_healthkit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:547ac283f84b5024be75290f351863f86eb48a950ec61e3150760230e6eba773", size = 20376 },
    { url = "https://files.pythonhosted.org/packages/9d/9f/0ff955096171e5d7d57ca0b879b8771f52cd0f1d4cf0726cdfc0064884f3/pyobjc_framework_healthkit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:c693725d8476b745232df90ef01487e75e1e1c448e599dd34adf3dce859de760", size = 20544 },
]

[[package]]
name = "pyobjc-framework-imagecapturecore"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/7b/3b/f4edbc58a8c7394393f8d00d0e764f655545e743ee4e33917f27b8c68e7b/pyobjc_framework_imagecapturecore-11.1.tar.gz", hash = "sha256:a610ceb6726e385b132a1481a68ce85ccf56f94667b6d6e1c45a2cfab806a624", size = 100398 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/61/62/54ed61e7cd3213549c8e98ca87a6b21afbb428d2c41948ae48ea019bf973/pyobjc_framework_imagecapturecore-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:ed296c23d3d8d1d9af96a6486d09fb8d294cc318e4a2152e6f134151c76065f8", size = 16021 },
    { url = "https://files.pythonhosted.org/packages/4e/91/71d48ec1b29d57112edd33ada86fcdbf1c9423ef2bdddadf8d37e8a03492/pyobjc_framework_imagecapturecore-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:ded8dc6a8c826a6ae1b6a6d0a31542bd1eb85345f86201689c54e51193b572dc", size = 16030 },
    { url = "https://files.pythonhosted.org/packages/c7/9d/7452fecf9b362b7a384b44256ca388b3e99905376e6f594565f2b2be0761/pyobjc_framework_imagecapturecore-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:254ae4502d651526c500533b8e2aee77ae7939f9acfd7d706dba2d464417deba", size = 16234 },
    { url = "https://files.pythonhosted.org/packages/f9/37/b7207fd6f8d9b55d642ad73850148ae68c4877f993c5ae2f7eac2578b991/pyobjc_framework_imagecapturecore-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:bab8ed798598ddaa53f5b39707b58e16a1b1152858c87fd3fa0d64081f0c0364", size = 16115 },
    { url = "https://files.pythonhosted.org/packages/6d/06/6eb5f2b1e2c8716ed07560055544f752ead2c2773dfc85cb24d9ec429b0e/pyobjc_framework_imagecapturecore-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:e01c29456d0560667f8fcd3ff2749e79ad51bf72512e699646ce32227f91b447", size = 16279 },
]

[[package]]
name = "pyobjc-framework-inputmethodkit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/02/32/6a90bba682a31960ba1fc2d3b263e9be26043c4fb7aed273c13647c8b7d9/pyobjc_framework_inputmethodkit-11.1.tar.gz", hash = "sha256:7037579524041dcee71a649293c2660f9359800455a15e6a2f74a17b46d78496", size = 27203 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/a8/0d/8a570072096fe339702e4ae9d98e59ee7c6c14124d4437c9a8c4482dda6d/pyobjc_framework_inputmethodkit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:dd0c591a9d26967018a781fa4638470147ef2a9af3ab4a28612f147573eeefba", size = 9489 },
    { url = "https://files.pythonhosted.org/packages/dc/a5/ce000bba1a52287c21d1d3aff6779a6bbb463da4337573cb17ecc9475939/pyobjc_framework_inputmethodkit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:5095005809a4108f362998b46994f99b5a57f9ba367c01141c1b9eaea311bc5b", size = 9508 },
    { url = "https://files.pythonhosted.org/packages/56/ad/bbdc9f4b91420a4d3cf0b633d1991d4ffb7bdeb78d01fa265bbd43fef929/pyobjc_framework_inputmethodkit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:013919a4d766a7e66045fa5dd5d819bfa0450ccb59baba2b89d7449bce637d6b", size = 9667 },
    { url = "https://files.pythonhosted.org/packages/13/92/d69e350213c242a2096f5708692effda0a0c96aab07410ecf582591b6f7f/pyobjc_framework_inputmethodkit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:2228bf58369351767294fe1aa400e98ec61e397a74a178788c24c98a1cff97ee", size = 9517 },
    { url = "https://files.pythonhosted.org/packages/7f/b0/c6ee5412bb402f9c8ac9a0bbd471f4fd57a1d2ca9510480cb67d12ebaa8d/pyobjc_framework_inputmethodkit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:92b9ce788ce4b094e352a64508050ff8e24307b8670d33488304b941d118894e", size = 9696 },
]

[[package]]
name = "pyobjc-framework-installerplugins"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/4d/89/9a881e466476ca21f3ff3e8e87ccfba1aaad9b88f7eea4be6d3f05b07107/pyobjc_framework_installerplugins-11.1.tar.gz", hash = "sha256:363e59c7e05553d881f0facd41884f17b489ff443d7856e33dd0312064c746d9", size = 27451 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/3d/01/45c3d159d671c5f488a40f70aa6791b8483a3ed32b461800990bb5ab4bb3/pyobjc_framework_installerplugins-11.1-py2.py3-none-any.whl", hash = "sha256:f92b06c9595f3c800b7aabf1c1a235bfb4b2de3f5406d5f604d8e2ddd0aecb4e", size = 4798 },
]

[[package]]
name = "pyobjc-framework-instantmessage"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://files.pythonhosted.org/packages/9f/b9/5cec4dd0053b5f63c01211a60a286c47464d9f3e0c81bd682e6542dbff00/pyobjc_framework_instantmessage-11.1.tar.gz", hash = "sha256:c222aa61eb009704b333f6e63df01a0e690136e7e495907e5396882779bf9525", size = 33774 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/91/34/acd618e90036822aaf01080d64558ba93e33e15ed91beb7d1d2aab290138/pyobjc_framework_instantmessage-11.1-py2.py3-none-any.whl", hash = "sha256:a70b716e279135eec5666af031f536c0f32dec57cfeae55cc9ff8457f10d4f3d", size = 5419 },
]

[[package]]
name = "pyobjc-framework-intents"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/4c/af/d7f260d06b79acca8028e373c2fe30bf0be014388ba612f538f40597d929/pyobjc_framework_intents-11.1.tar.gz", hash = "sha256:13185f206493f45d6bd2d4903c2136b1c4f8b9aa37628309ace6ff4a906b4695", size = 448459 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/8a/37/e6fa5737da42fb1265041bd3bd4f2be96f09294018fabf07139dd9dbc7b9/pyobjc_framework_intents-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:a663e2de1b7ae7b547de013f89773963f8180023e36f2cebfe8060395dc34c33", size = 32253 },
    { url = "https://files.pythonhosted.org/packages/f0/ff/f793a0c4b5ea87af3fc228d74e457c1594695b2745b3007a8ef4832ebeb7/pyobjc_framework_intents-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:9e21b3bc33de2d5f69b5c1d581e5c724a08686fe84ec324a4be365bef769e482", size = 32266 },
    { url = "https://files.pythonhosted.org/packages/52/e9/2725ae5f990faa7d7909e6ac14d14034d1e70028080ed602a03aa715b4bc/pyobjc_framework_intents-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:e008d542abe38fd374c9ada7c833ad6e34a2db92b4dcbfba0a59ff830b9093bc", size = 32499 },
    { url = "https://files.pythonhosted.org/packages/90/47/d934ec7c514cc59b53da271f172cf6fd30e9a63aa960580a751d4960d495/pyobjc_framework_intents-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:55498040123904b685cd38555eb84d95833fcb467b497d31757d6ac648a11817", size = 32506 },
    { url = "https://files.pythonhosted.org/packages/95/f1/acbda130f45e38f35fca2aa381f4da9ed72e36c4c784395ddb3fea511391/pyobjc_framework_intents-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:4e3ec70c02d3166088223938a7433e479659cbd8ce04be5bf515ea8d6e3c353d", size = 32742 },
]

[[package]]
name = "pyobjc-framework-intentsui"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-intents" },
]
sdist = { url = "https://files.pythonhosted.org/packages/86/46/20aae4a71efb514b096f36273a6129b48b01535bf501e5719d4a97fcb3a5/pyobjc_framework_intentsui-11.1.tar.gz", hash = "sha256:c8182155af4dce369c18d6e6ed9c25bbd8110c161ed5f1b4fb77cf5cdb99d135", size = 21305 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/43/7c/77fbd2a6f85eb905fbf27ba7540eaf2a026771ed5100fb1c01143cf47e9b/pyobjc_framework_intentsui-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:99a3ae40eb2a6ef1125955dd513c8acc88ce7d8d90130a8cdeaec8336e6fbec5", size = 8965 },
    { url = "https://files.pythonhosted.org/packages/9b/d6/ce8e2f6354bd77271b8f9f2a05920fb0a6de57ab5d97033021672853acb5/pyobjc_framework_intentsui-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:154fd92112184e8ef29ce81e685c377422dffcff4f7900ea6e5956a0e2be2268", size = 8983 },
    { url = "https://files.pythonhosted.org/packages/e1/2b/562785a91c30eccd3eea28ea02b31a029e04ecc5e994da7cd60205baf250/pyobjc_framework_intentsui-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:6d7d5402c05840a45047cf905fa550c2898cf5580cdee00a36bd35dd624c7542", size = 9154 },
    { url = "https://files.pythonhosted.org/packages/94/30/069cf617e514434304ea0b1e8227d653af192c6dc7062f2e97ab0204e449/pyobjc_framework_intentsui-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:35ef9f190f480147ce797809a63cc2b5f2ea64b51255d691e5e94bd8337e01ef", size = 9029 },
    { url = "https://files.pythonhosted.org/packages/7a/77/6830682e3d7b9fdbead08f9053d714336f1cf5c6c6170d91b9cc266d243f/pyobjc_framework_intentsui-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:1bd950f808efb7ba7fbbc977300d7932a1dad41fbd3c78c8002870ca602e22d5", size = 9232 },
]

[[package]]
name = "pyobjc-framework-iobluetooth"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/93/e0/74b7b10c567b66c5f38b45ab240336325a4c889f43072d90f2b90aaeb7c0/pyobjc_framework_iobluetooth-11.1.tar.gz", hash = "sha256:094fd4be60cd1371b17cb4b33a3894e0d88a11b36683912be0540a7d51de76f1", size = 300992 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/da/94/eef57045762e955795a4e3312674045c52f8c506133acf9efe1b3370b93f/pyobjc_framework_iobluetooth-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:883781e7223cb0c63fab029d640721ded747f2e2b067645bc8b695ef02a4a4dd", size = 40406 },
    { url = "https://files.pythonhosted.org/packages/ed/f5/24476d6919c2d8d849c88740e81f620663181b3c97ac6e3aaeb1833277a5/pyobjc_framework_iobluetooth-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:4a8b1caba9ac51435f64a6cf9c1a2be867603161af8bebdd1676072ebed2fed9", size = 40428 },
    { url = "https://files.pythonhosted.org/packages/57/b6/ced1b076a86ea3d7a685155e8c61ab9ecf8037d2b5401d4aae65014789b3/pyobjc_framework_iobluetooth-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:2c99ade82a79263ea71c51d430696a2ad155beb01a67df59d52be63e181e0482", size = 40626 },
    { url = "https://files.pythonhosted.org/packages/d2/a2/0567b8b6e5bb75f7172495890a7746a986fd46a436e5f1ca7abc386bbbdc/pyobjc_framework_iobluetooth-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:2ef72cef1e03468e91a2f01af2390143bd6e4fcad1c6d0494dd857c99fa0d1a7", size = 40478 },
    { url = "https://files.pythonhosted.org/packages/18/eb/b148fba594890aec937bf3a87b61a385918f2bee4394763595e59a9f39a0/pyobjc_framework_iobluetooth-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:a9a7e11a4bbb4a364b0412ca8632a1e853270c98c24d28421133f69c0c0ecaff", size = 40690 },
]

[[package]]
name = "pyobjc-framework-iobluetoothui"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-iobluetooth" },
]
sdist = { url = "https://files.pythonhosted.org/packages/dd/32/872272faeab6fe471eac6962c75db72ce65c3556e00b4edebdb41aaab7cb/pyobjc_framework_iobluetoothui-11.1.tar.gz", hash = "sha256:060c721f1cd8af4452493e8153b72b572edcd2a7e3b635d79d844f885afee860", size = 22835 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/d6/ed/35efed52ed3fa698480624e49ee5f3d859827aad5ff1c7334150c695e188/pyobjc_framework_iobluetoothui-11.1-py2.py3-none-any.whl", hash = "sha256:3c5a382d81f319a1ab9ab11b7ead04e53b758fdfeb604755d39c3039485eaac6", size = 4026 },
]

[[package]]
name = "pyobjc-framework-iosurface"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/c5/ce/38ec17d860d0ee040bb737aad8ca7c7ff46bef6c9cffa47382d67682bb2d/pyobjc_framework_iosurface-11.1.tar.gz", hash = "sha256:a468b3a31e8cd70a2675a3ddc7176ab13aa521c035f11188b7a3af8fff8b148b", size = 20275 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/1d/26/fa912d397b577ee318b20110a3c959e898514a1dce19b4f13f238a31a677/pyobjc_framework_iosurface-11.1-py2.py3-none-any.whl", hash = "sha256:0c36ad56f8ec675dd07616418a2bc29126412b54627655abd21de31bcafe2a79", size = 4948 },
]

[[package]]
name = "pyobjc-framework-ituneslibrary"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/ee/43/aebefed774b434965752f9001685af0b19c02353aa7a12d2918af0948181/pyobjc_framework_ituneslibrary-11.1.tar.gz", hash = "sha256:e2212a9340e4328056ade3c2f9d4305c71f3f6af050204a135f9fa9aa3ba9c5e", size = 47388 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/2a/57/a29150f734b45b7408cc06efb9e2156328ae74624e5c4a7fe95118e13e94/pyobjc_framework_ituneslibrary-11.1-py2.py3-none-any.whl", hash = "sha256:4e87d41f82acb6d98cf70ac3c932a568ceb3c2035383cbf177f54e63de6b815f", size = 5191 },
]

[[package]]
name = "pyobjc-framework-kernelmanagement"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/1a/b6/708f10ac16425834cb5f8b71efdbe39b42c3b1009ac0c1796a42fc98cd36/pyobjc_framework_kernelmanagement-11.1.tar.gz", hash = "sha256:e934d1638cd89e38d6c6c5d4d9901b4295acee2d39cbfe0bd91aae9832961b44", size = 12543 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/b9/cf/17ff988ad1a0e55a4be5336c64220aa620ad19bb2f487a1122e9a864b29e/pyobjc_framework_kernelmanagement-11.1-py2.py3-none-any.whl", hash = "sha256:ec74690bd3383a7945c4a038cc4e1553ec5c1d2408b60e2b0003a3564bff7c47", size = 3656 },
]

[[package]]
name = "pyobjc-framework-latentsemanticmapping"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/db/8a/4e54ee2bc77d59d770b287daf73b629e2715a2b3b31264d164398131cbad/pyobjc_framework_latentsemanticmapping-11.1.tar.gz", hash = "sha256:c6c3142301e4d375c24a47dfaeebc2f3d0fc33128a1c0a755794865b9a371145", size = 17444 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/2c/50/d62815b02968236eb46c33f0fb0f7293a32ef68d2ec50c397140846d4e42/pyobjc_framework_latentsemanticmapping-11.1-py2.py3-none-any.whl", hash = "sha256:57f3b183021759a100d2847a4d8aa314f4033be3d2845038b62e5e823d96e871", size = 5454 },
]

[[package]]
name = "pyobjc-framework-launchservices"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-coreservices" },
]
sdist = { url = "https://files.pythonhosted.org/packages/2b/0a/a76b13109b8ab563fdb2d7182ca79515f132f82ac6e1c52351a6b02896a8/pyobjc_framework_launchservices-11.1.tar.gz", hash = "sha256:80b55368b1e208d6c2c58395cc7bc12a630a2a402e00e4930493e9bace22b7bb", size = 20446 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/12/30/a4de9021fdef7db0b224cdc1eae75811d889dc1debdfafdabf8be7bd0fb9/pyobjc_framework_launchservices-11.1-py2.py3-none-any.whl", hash = "sha256:8b58f1156651058b2905c87ce48468f4799db86a7edf760e1897fedd057a3908", size = 3889 },
]

[[package]]
name = "pyobjc-framework-libdispatch"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/be/89/7830c293ba71feb086cb1551455757f26a7e2abd12f360d375aae32a4d7d/pyobjc_framework_libdispatch-11.1.tar.gz", hash = "sha256:11a704e50a0b7dbfb01552b7d686473ffa63b5254100fdb271a1fe368dd08e87", size = 53942 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/ac/92/ff9ceb14e1604193dcdb50643f2578e1010c68556711cd1a00eb25489c2b/pyobjc_framework_libdispatch-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:dc9a7b8c2e8a63789b7cf69563bb7247bde15353208ef1353fff0af61b281684", size = 15627 },
    { url = "https://files.pythonhosted.org/packages/0f/10/5851b68cd85b475ff1da08e908693819fd9a4ff07c079da9b0b6dbdaca9c/pyobjc_framework_libdispatch-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:c4e219849f5426745eb429f3aee58342a59f81e3144b37aa20e81dacc6177de1", size = 15648 },
    { url = "https://files.pythonhosted.org/packages/1b/79/f905f22b976e222a50d49e85fbd7f32d97e8790dd80a55f3f0c305305c32/pyobjc_framework_libdispatch-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:a9357736cb47b4a789f59f8fab9b0d10b0a9c84f9876367c398718d3de085888", size = 15912 },
    { url = "https://files.pythonhosted.org/packages/ee/b0/225a3645ba2711c3122eec3e857ea003646643b4122bd98db2a8831740ff/pyobjc_framework_libdispatch-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:cd08f32ea7724906ef504a0fd40a32e2a0be4d64b9239530a31767ca9ccfc921", size = 15655 },
    { url = "https://files.pythonhosted.org/packages/e2/b5/ff49fb81f13c7ec48cd7ccad66e1986ccc6aa1984e04f4a78074748f7926/pyobjc_framework_libdispatch-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:5d9985b0e050cae72bf2c6a1cc8180ff4fa3a812cd63b2dc59e09c6f7f6263a1", size = 15920 },
]

[[package]]
name = "pyobjc-framework-libxpc"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/6a/c9/7e15e38ac23f5bfb4e82bdf3b7ef88e2f56a8b4ad884009bc2d5267d2e1f/pyobjc_framework_libxpc-11.1.tar.gz", hash = "sha256:8fd7468aa520ff19915f6d793070b84be1498cb87224bee2bad1f01d8375273a", size = 49135 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/be/8f/dfd8e1e1e461f857a1e50138e69b17c0e62a8dcaf7dea791cc158d2bf854/pyobjc_framework_libxpc-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:c29b2df8d74ff6f489afa7c39f7c848c5f3d0531a6bbe704571782ee6c820084", size = 19573 },
    { url = "https://files.pythonhosted.org/packages/00/fa/9ac86892294428a0eb532242a6fcbec565d0cf0e919924b6b7c064c8b196/pyobjc_framework_libxpc-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:6862e63f565823d4eeb56f18f90a3ee8682c52a8d4bcd486d3535c9959464eda", size = 19578 },
    { url = "https://files.pythonhosted.org/packages/44/2c/0b0bdc7847adf6ed653e846a98685346f70b1aaa187e37ddff2641cc54e2/pyobjc_framework_libxpc-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:2df539d11b65e229f8436a3660d0d1dce2cc7ba571054c5b91350b836db22576", size = 20167 },
    { url = "https://files.pythonhosted.org/packages/13/f0/b44b1b094eafe62d3af6e13098eae1f2a9a863661d3d60745a6a0b91b4c4/pyobjc_framework_libxpc-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:4f3083fde3c366cc58bcdb2c183fae9c531fb556d35a495818019f1a5d85c24d", size = 19291 },
    { url = "https://files.pythonhosted.org/packages/7f/e4/9b7d86a0aa15ef3b6893238d7634dcfc08b6a800cd61d8a607055224c955/pyobjc_framework_libxpc-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:654db8e822e60a1246d4d55c7127a140e10d6faa0da5a7366a16cc10def44deb", size = 19868 },
]

[[package]]
name = "pyobjc-framework-linkpresentation"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://files.pythonhosted.org/packages/b9/76/22873be73f12a3a11ae57af13167a1d2379e4e7eef584de137156a00f5ef/pyobjc_framework_linkpresentation-11.1.tar.gz", hash = "sha256:a785f393b01fdaada6d7d6d8de46b7173babba205b13b44f1dc884b3695c2fc9", size = 14987 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/3d/59/23249e76e06e3c1a4f88acac7144999fae5a5a8ce4b90272d08cc0ac38ae/pyobjc_framework_linkpresentation-11.1-py2.py3-none-any.whl", hash = "sha256:018093469d780a45d98f4e159f1ea90771caec456b1599abcc6f3bf3c6873094", size = 3847 },
]

[[package]]
name = "pyobjc-framework-localauthentication"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-security" },
]
sdist = { url = "https://files.pythonhosted.org/packages/e5/27/9e3195f3561574140e9b9071a36f7e0ebd18f50ade9261d23b5b9df8fccd/pyobjc_framework_localauthentication-11.1.tar.gz", hash = "sha256:3cd48907c794bd414ac68b8ac595d83c7e1453b63fc2cfc2d2035b690d31eaa1", size = 40700 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/91/db/59f118cc2658814c6b501b7360ca4fe6a82fd289ced5897b99787130ceef/pyobjc_framework_localauthentication-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:aa3815f936612d78e51b53beed9115c57ae2fd49500bb52c4030a35856e6569e", size = 10730 },
    { url = "https://files.pythonhosted.org/packages/9f/8b/544cadc6ecf75def347e96cdae4caa955bc23f2bc314779cffe1e6ba9475/pyobjc_framework_localauthentication-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:9c9446c017b13c8dcadf485b76ab1d7bc12099b504bf5c2df1aae33b5dc4ab2c", size = 10748 },
    { url = "https://files.pythonhosted.org/packages/44/f9/4095b2caa4453971bd790b6aeda05967c22743e1f80e5bf6cb63ec419288/pyobjc_framework_localauthentication-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:d5a2e1ea2fe8233dc244f6029d5d0c878102b2e0615cb4b81b2f30d9ee101fca", size = 10896 },
    { url = "https://files.pythonhosted.org/packages/dd/0a/fd8cfcfd761792fd482b49d08f5a0bf6540ebb3de6baacb4a5de5c5ed635/pyobjc_framework_localauthentication-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:f49c9dbbecfa0b0a7a633c60bda8179575e3685b6a696658a835c63afee90f9a", size = 10786 },
    { url = "https://files.pythonhosted.org/packages/ec/87/5204ea53e0a945877c650205841f766bc7fca55ad81cd5bcb0a966fcdaa4/pyobjc_framework_localauthentication-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:e41be8e2132d1517e597401c7858b22531db2e7760d898993acc03ea13edb834", size = 10930 },
]

[[package]]
name = "pyobjc-framework-localauthenticationembeddedui"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-localauthentication" },
]
sdist = { url = "https://files.pythonhosted.org/packages/29/7b/08c1e52487b07e9aee4c24a78f7c82a46695fa883113e3eece40f8e32d40/pyobjc_framework_localauthenticationembeddedui-11.1.tar.gz", hash = "sha256:22baf3aae606e5204e194f02bb205f244e27841ea7b4a4431303955475b4fa56", size = 14076 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/51/3d/2aaa3a4f0e82f0ac95cc432a6079f6dc20aa18a66c9a87ac6128c70df9ef/pyobjc_framework_localauthenticationembeddedui-11.1-py2.py3-none-any.whl", hash = "sha256:3539a947b102b41ea6e40e7c145f27280d2f36a2a9a1211de32fa675d91585eb", size = 3973 },
]

[[package]]
name = "pyobjc-framework-mailkit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/7e/7e/f22d733897e7618bd70a658b0353f5f897c583df04e7c5a2d68b99d43fbb/pyobjc_framework_mailkit-11.1.tar.gz", hash = "sha256:bf97dc44cb09b9eb9d591660dc0a41f077699976144b954caa4b9f0479211fd7", size = 32012 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/bf/23/1897fc071e8e71bc0bef53bcb0d600eb1ed3bd6c4609f7257ddfe151d37a/pyobjc_framework_mailkit-11.1-py2.py3-none-any.whl", hash = "sha256:8e6026462567baba194468e710e83787f29d9e8c98ea0583f7b401ea9515966e", size = 4854 },
]

[[package]]
name = "pyobjc-framework-mapkit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-corelocation" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://files.pythonhosted.org/packages/57/f0/505e074f49c783f2e65ca82174fd2d4348568f3f7281c1b81af816cf83bb/pyobjc_framework_mapkit-11.1.tar.gz", hash = "sha256:f3a5016f266091be313a118a42c0ea4f951c399b5259d93639eb643dacc626f1", size = 165614 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/30/0a/50aa2fba57499ff657cacb9ef1730006442e4f42d9a822dae46239603ecc/pyobjc_framework_mapkit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:91976c6dbc8cbb020e059a0ccdeab8933184712f77164dbad5a5526c1a49599d", size = 22515 },
    { url = "https://files.pythonhosted.org/packages/78/54/792f4d5848176753bfde8f10ac21b663981adf940243765edad45908cd55/pyobjc_framework_mapkit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:0b6fa1c4fffc3ae91adb965731a0cc943b3b6e82c8f21919a53a68b43a67b534", size = 22534 },
    { url = "https://files.pythonhosted.org/packages/07/0c/fd03986fc74c5e523e5ba824d3b4f0fd1f4a52720f28da93499787960317/pyobjc_framework_mapkit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:1dc27d315849ac96647d13c82eeefce5d1d2db8c64767ce10bd3e77cbaad2291", size = 22759 },
    { url = "https://files.pythonhosted.org/packages/15/e3/6040945ad0bfb9a065d007a5e16b07f8ae0423fcf4e097eba92eb8a143bb/pyobjc_framework_mapkit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:fb9b1d8cd5c0e8a097438369771d296de808621bc6013aa0065bc83716f5bdb0", size = 22657 },
    { url = "https://files.pythonhosted.org/packages/e2/07/eca78e240aa13c4e32ac4c6db158e059f375a2d240928e42c8e77f348ef0/pyobjc_framework_mapkit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:fe4581f5370dc7a209c1135e9c664a5a78950d3f5c39613bfb15c1e02a6258f3", size = 22886 },
]

[[package]]
name = "pyobjc-framework-mediaaccessibility"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/8d/81/60412b423c121de0fa0aa3ef679825e1e2fe8b00fceddec7d72333ef564b/pyobjc_framework_mediaaccessibility-11.1.tar.gz", hash = "sha256:52479a998fec3d079d2d4590a945fc78c41fe7ac8c76f1964c9d8156880565a4", size = 18440 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/99/a1/f4cbdf8478ad01859e2c8eef08e28b8a53b9aa4fe5d238a86bad29b73555/pyobjc_framework_mediaaccessibility-11.1-py2.py3-none-any.whl", hash = "sha256:cd07e7fc375ff1e8d225e0aa2bd9c2c1497a4d3aa5a80bfb13b08800fcd7f034", size = 4691 },
]

[[package]]
name = "pyobjc-framework-mediaextension"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-avfoundation" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-coremedia" },
]
sdist = { url = "https://files.pythonhosted.org/packages/e1/09/fd214dc0cf3f3bc3f528815af4799c0cb7b4bf4032703b19ea63486a132b/pyobjc_framework_mediaextension-11.1.tar.gz", hash = "sha256:85a1c8a94e9175fb364c453066ef99b95752343fd113f08a3805cad56e2fa709", size = 58489 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/56/78/2c2d8265851f6060dbf4434c21bd67bf569b8c3071ba1f257e43aae563a8/pyobjc_framework_mediaextension-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:06cb19004413a4b08dd75cf1e5dadea7f2df8d15feeeb7adb529d0cf947fa789", size = 38859 },
    { url = "https://files.pythonhosted.org/packages/e7/6b/1d3761316ca7df57700a68b28f7c00cc4f050b3f6debac2305219506d6b1/pyobjc_framework_mediaextension-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:40f1440ccc8da6deb80810866f8c807c17567db67b53e1576ea3a3b1330c85f9", size = 38870 },
    { url = "https://files.pythonhosted.org/packages/15/e3/48f4ba724e31cb7adeaf5f9198ad5ab9cab45bcfc358b8af5759d8f79971/pyobjc_framework_mediaextension-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:29edab42d9ecd394ac26f2ae2dfd7e2118452fc60a5623843919c1e9659c9dbc", size = 39104 },
    { url = "https://files.pythonhosted.org/packages/a7/f8/65cfc9e9be245a7524572b64655d809c9294ded599ebf068c7c1b73c6ecf/pyobjc_framework_mediaextension-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:5efd284932ed0e7cfbca90a142b84a3966c73e51308688f8c230af41f9fb8c39", size = 38925 },
    { url = "https://files.pythonhosted.org/packages/68/99/bdc2fa27576302b6b3a5b018579637251e4ba4620505254e7ebd79134ad1/pyobjc_framework_mediaextension-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:ca3a3ef1f3a759b53f297ccd701d29091eec66cc629a2b48c9acbe6c297bf256", size = 39142 },
]

[[package]]
name = "pyobjc-framework-medialibrary"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://files.pythonhosted.org/packages/2b/06/11ff622fb5fbdd557998a45cedd2b0a1c7ea5cc6c5cb015dd6e42ebd1c41/pyobjc_framework_medialibrary-11.1.tar.gz", hash = "sha256:102f4326f789734b7b2dfe689abd3840ca75a76fb8058bd3e4f85398ae2ce29d", size = 18706 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/62/2b/a4200080d97f88fdd406119bb8f00ccb7f32794f84735485510c14e87e76/pyobjc_framework_medialibrary-11.1-py2.py3-none-any.whl", hash = "sha256:779be84bd280f63837ce02028ca46b41b090902aa4205887ffd5777f49377669", size = 4340 },
]

[[package]]
name = "pyobjc-framework-mediaplayer"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-avfoundation" },
]
sdist = { url = "https://files.pythonhosted.org/packages/80/d5/daba26eb8c70af1f3823acfd7925356acc4dd75eeac4fc86dc95d94d0e15/pyobjc_framework_mediaplayer-11.1.tar.gz", hash = "sha256:d07a634b98e1b9eedd82d76f35e616525da096bd341051ea74f0971e0f2f2ddd", size = 93749 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/2b/aa/b37aac80d821bd2fa347ddad1f6c7c75b23155e500edf1cb3b3740c27036/pyobjc_framework_mediaplayer-11.1-py2.py3-none-any.whl", hash = "sha256:b655cf537ea52d73209eb12935a047301c30239b318a366600f0f44335d51c9a", size = 6960 },
]

[[package]]
name = "pyobjc-framework-mediatoolbox"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/e1/68/cc230d2dfdeb974fdcfa828de655a43ce2bf4962023fd55bbb7ab0970100/pyobjc_framework_mediatoolbox-11.1.tar.gz", hash = "sha256:97834addc5179b3165c0d8cd74cc97ad43ed4c89547724216426348aca3b822a", size = 23568 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/b5/23/6b5d999e1e71c42d5d116d992515955ac1bbc5cf4890072bb26f38eb9802/pyobjc_framework_mediatoolbox-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:2867c91645a335ee29b47e9c0e9fd3ea8c9daad0c0719c50b8bf244d76998056", size = 12785 },
    { url = "https://files.pythonhosted.org/packages/29/05/24d60869a816418771653057720727d6df2dd8485302a21f80cfcb694110/pyobjc_framework_mediatoolbox-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:bf26348d20caef38efb9cfc02d28af83c930b2f2c9581407f8ec04b3d8321a7a", size = 12794 },
    { url = "https://files.pythonhosted.org/packages/37/c5/7b2950c22187c1a2e4f492684c34dd0cd230b8be4c7749e4b223b7769def/pyobjc_framework_mediatoolbox-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:975de470af8e52104bd1548eb9b4b0ef98524f35a6263c0bb4182797b9c5975b", size = 13394 },
    { url = "https://files.pythonhosted.org/packages/d8/b4/f3b9944cb80bb5e72f3550ddfe6ba9fca81eefcb75abbf3410b304e0b1ca/pyobjc_framework_mediatoolbox-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:d781e45fb1a7e532bcbae38c0f491629eaa641cdc226019544123b51794baf34", size = 12775 },
    { url = "https://files.pythonhosted.org/packages/d3/6b/22f33982711fe787b2808530365afa2d4663d231200de51013cccc4cec46/pyobjc_framework_mediatoolbox-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:e30fd2ffdea1b2c7c314d07266bce7614197c2b3ffd5b09f7012e7df7aa5c7a6", size = 13379 },
]

[[package]]
name = "pyobjc-framework-metal"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/af/cf/29fea96fd49bf72946c5dac4c43ef50f26c15e9f76edd6f15580d556aa23/pyobjc_framework_metal-11.1.tar.gz", hash = "sha256:f9fd3b7574a824632ee9b7602973da30f172d2b575dd0c0f5ef76b44cfe9f6f9", size = 446549 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/4c/94/3d5a8bed000dec4a13e72dde175898b488192716b7256a05cc253c77020d/pyobjc_framework_metal-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:1f3aae0f9a4192a7f4f158dbee126ab5ef63a81bf9165ec63bc50c353c8d0e6f", size = 57969 },
    { url = "https://files.pythonhosted.org/packages/4f/af/b1f78770bb4b8d73d7a70140e39ca92daa2ba6b8de93d52b2ebf9db7d03e/pyobjc_framework_metal-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:d9b24d0ddb98b34a9a19755e5ca507c62fcef40ee5eae017e39be29650137f8c", size = 57994 },
    { url = "https://files.pythonhosted.org/packages/97/93/e680c0ece0e21cb20bc5d0504acd96ca6828fc766b8ed624d69230c1796d/pyobjc_framework_metal-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:de71b46062cb533be2c025cd6018fd4db9d7fd6a65bd67131d8e484c3616321a", size = 58381 },
    { url = "https://files.pythonhosted.org/packages/22/f0/b7c636729ed75d05bbb236b3b813d7629ffad5fb5951710978a478ac7713/pyobjc_framework_metal-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:b4c4dcab1db5750575a49a0a903528ea64b5bb93a9f3aaac5c810117a9c07e9c", size = 58824 },
    { url = "https://files.pythonhosted.org/packages/dc/22/8683231702db8a585c83db38cf9e76de2272673e7230de715ff3a868d0dc/pyobjc_framework_metal-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:432fefd3b27ab58c703b2f07afbc4690af815a9a8b4f8a997c4aefa8652e71d7", size = 59221 },
]

[[package]]
name = "pyobjc-framework-metalfx"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-metal" },
]
sdist = { url = "https://files.pythonhosted.org/packages/10/20/4c839a356b534c161fb97e06589f418fc78cc5a0808362bdecf4f9a61a8d/pyobjc_framework_metalfx-11.1.tar.gz", hash = "sha256:555c1b895d4ba31be43930f45e219a5d7bb0e531d148a78b6b75b677cc588fd8", size = 27002 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/36/73/a8df8fa445a09fbc917a495a30b13fbcf224b5576c1e464d5ece9824a493/pyobjc_framework_metalfx-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:60e1dcdf133d2504d810c3a9ba5a02781c9d54c2112a9238de8e3ca4e8debf31", size = 10107 },
    { url = "https://files.pythonhosted.org/packages/8e/7b/4d925bf5f1f0b0d254b3167999987ecafb251f589cd863bdbaf96eb4ad2a/pyobjc_framework_metalfx-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:fdced91f6b2012c556db954de0e17f6d7985d52b4af83262f4d083bcd87aa01c", size = 10122 },
    { url = "https://files.pythonhosted.org/packages/0c/b3/633bbd87f9380f8e288d02b44e70845453daf640602d15c4e167536c4b45/pyobjc_framework_metalfx-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:e1b2819bd6a66ba55fb7019b45d38a803ea21b8258fa41c8e9ad7c28cfe74092", size = 10284 },
    { url = "https://files.pythonhosted.org/packages/03/87/2d9ac114e454575daf81a69da8e6170f0d357de3922b50e5ca5ca0968e30/pyobjc_framework_metalfx-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:aedfee1218b5784b010d618332a2cc088ba2ff9414eaa06e5db465eb5ef0aa43", size = 10315 },
    { url = "https://files.pythonhosted.org/packages/69/c6/98787a080b585306101e8b56f6f0bb1c579ed8f1981e9b0362a84046ec48/pyobjc_framework_metalfx-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:934cbc969182c57f5094389fe4afe6695595757d0d61f1ab663257475fdcc593", size = 10473 },
]

[[package]]
name = "pyobjc-framework-metalkit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-metal" },
]
sdist = { url = "https://files.pythonhosted.org/packages/45/cb/7e01bc61625c7a6fea9c9888c9ed35aa6bbc47cda2fcd02b6525757bc2b8/pyobjc_framework_metalkit-11.1.tar.gz", hash = "sha256:8811cd81ee9583b9330df4f2499a73dcc53f3359cb92767b409acaec9e4faa1e", size = 45135 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/87/0c/516b6d7a67a170b7d2316701d5288797a19dd283fcc2f73b7b78973e1392/pyobjc_framework_metalkit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:4854cf74fccf6ce516b49bf7cf8fc7c22da9a3743914a2f4b00f336206ad47ec", size = 8730 },
    { url = "https://files.pythonhosted.org/packages/11/2a/5c55d1e57d8e90613fbce4b204b7d94a9ae7019a0928cb50cbd60bfa8191/pyobjc_framework_metalkit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:62e261b7798b276fee1fee065030a5d19d173863e9c697a80d1fc9a22258ec2c", size = 8749 },
    { url = "https://files.pythonhosted.org/packages/b6/e4/7b7b61d72fa235c9e364117a595c621c427217567d300da21d7417668c46/pyobjc_framework_metalkit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:b8a378135566e3c48838c19044e17ed2598a4050516ee1c23eee7d42439ef3c8", size = 8903 },
    { url = "https://files.pythonhosted.org/packages/8a/cf/103d3233fcf2ff9ae23d5d143fde7a0d1308026ca46a35f23cffa83e6915/pyobjc_framework_metalkit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:ce886f3966144774d9222148eaf29fb08097d7dab5658186ded597b7c088f927", size = 8786 },
    { url = "https://files.pythonhosted.org/packages/96/63/748c15b5aa70a61c6735018d55b7a22560032f2ab060ee13349ae0aaef9c/pyobjc_framework_metalkit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:3e0776886fcd79fe7f0c55c718ebcdf073ac3e05d03040ab284ee09902fe1c70", size = 8948 },
]

[[package]]
name = "pyobjc-framework-metalperformanceshaders"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-metal" },
]
sdist = { url = "https://files.pythonhosted.org/packages/d0/11/5df398a158a6efe2c87ac5cae121ef2788242afe5d4302d703147b9fcd91/pyobjc_framework_metalperformanceshaders-11.1.tar.gz", hash = "sha256:8a312d090a0f51651e63d9001e6cc7c1aa04ceccf23b494cbf84b7fd3d122071", size = 302113 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/89/df/f844516a54ef0fa1d047fe5fd94b63bc8b1218c09f7d4309b2a67a79708d/pyobjc_framework_metalperformanceshaders-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:06b2a4e446fe859e30f7efc7ccfbaefd443225a6ec53d949a113a6a4acc16c4c", size = 32888 },
    { url = "https://files.pythonhosted.org/packages/b5/a2/5387ab012a20afb7252b3938a8fb5319c946a3faaa9166b79b51ab3c0bf6/pyobjc_framework_metalperformanceshaders-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:97be4bd0ded06c663205bd1cf821e148352346f147da48dba44cf7680f0ea23b", size = 32903 },
    { url = "https://files.pythonhosted.org/packages/ee/8c/5f10387b638a92ffbc3ccd04bac73c68a5119672b908b6dc90d46e30fd40/pyobjc_framework_metalperformanceshaders-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:c905a3f5a34a95c1fd26bf07da505ed84b9b0a0c88a8f004914d9173f5037142", size = 33093 },
    { url = "https://files.pythonhosted.org/packages/69/69/9308e2d635f1b48c373601b26a9db9df4cdbe42ad64b72d7f147b662db65/pyobjc_framework_metalperformanceshaders-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:21ca31e4246e491df788f00978744d37db975266065f7ccbf393f027b4c6e248", size = 33012 },
    { url = "https://files.pythonhosted.org/packages/2f/e6/5dfedd36c6a817afeebebe7cf748e7820df9796ca685b41b66cc09602888/pyobjc_framework_metalperformanceshaders-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:c651e62ce58e75a88cfd287357fdd8d9a7f729c87248c8f43ce16025986afe6a", size = 33221 },
]

[[package]]
name = "pyobjc-framework-metalperformanceshadersgraph"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-metalperformanceshaders" },
]
sdist = { url = "https://files.pythonhosted.org/packages/32/c3/8d98661f7eecd1f1b0d80a80961069081b88efd3a82fbbed2d7e6050c0ad/pyobjc_framework_metalperformanceshadersgraph-11.1.tar.gz", hash = "sha256:d25225aab4edc6f786b29fe3d9badc4f3e2d0caeab1054cd4f224258c1b6dbe2", size = 105098 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/0d/a1/2033cf8b0d9f059e3495a1d9a691751b242379c36dd5bcb96c8edb121c9e/pyobjc_framework_metalperformanceshadersgraph-11.1-py2.py3-none-any.whl", hash = "sha256:9b8b014e8301c2ae608a25f73bbf23c8f3f73a6f5fdbafddad509a21b84df681", size = 6461 },
]

[[package]]
name = "pyobjc-framework-metrickit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/bd/48/8ae969a51a91864000e39c1de74627b12ff587b1dbad9406f7a30dfe71f8/pyobjc_framework_metrickit-11.1.tar.gz", hash = "sha256:a79d37575489916c35840e6a07edd958be578d3be7a3d621684d028d721f0b85", size = 40952 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/55/d1/aea4655e7eaa9ab19da8fe78ab363270443059c8a542b8f8a071b4988b57/pyobjc_framework_metrickit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:a034e6b982e915da881edef87d71b063e596511d52aef7a32c683571f364156e", size = 8081 },
    { url = "https://files.pythonhosted.org/packages/d9/d2/1f70e7524f6aca2e7aa7a99c4024d8c7e7cdd2ae9b338d2958548ee432c0/pyobjc_framework_metrickit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:95e98e96b8f122b0141e84f13ae9e0f91d09d0803b1c093fdc7d19123f000f9e", size = 8104 },
    { url = "https://files.pythonhosted.org/packages/aa/26/d875ea9da12be79e5336e7aa9134db97eb917c968f8237235e5a70da0b72/pyobjc_framework_metrickit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:14de8dcaa107fe15546df91b1f7d51dc398169c3d1b06e02291fdb8722c6bf41", size = 8247 },
    { url = "https://files.pythonhosted.org/packages/18/ae/d54e66860cb083638f0dbf8e60b71931f0357c55a7eca7c25a3198c0a561/pyobjc_framework_metrickit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:75c5a62abc535387eea6a1e1612cfa5b1d59512ebfa8a3352596d481b18cc714", size = 8150 },
    { url = "https://files.pythonhosted.org/packages/ef/cf/f9c1ec5241c3ffb999b6eb026df260f0336300a13324eb53e2bf44701ec0/pyobjc_framework_metrickit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:92483af233a2c31ef73dd0f7a32988a323f9560699f2f1c6c10a8a282a7b9cfd", size = 8296 },
]

[[package]]
name = "pyobjc-framework-mlcompute"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/8b/e6/f064dec650fb1209f41aba0c3074416cb9b975a7cf4d05d93036e3d917f0/pyobjc_framework_mlcompute-11.1.tar.gz", hash = "sha256:f6c4c3ea6a62e4e3927abf9783c40495aa8bb9a8c89def744b0822da58c2354b", size = 89021 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/23/cc/f47a4ac2d1a792b82206fdab58cc61b3aae15e694803ea2c81f3dfc16d9d/pyobjc_framework_mlcompute-11.1-py2.py3-none-any.whl", hash = "sha256:975150725e919f8d3d33f830898f3cd2fd19a440999faab320609487f4eae19d", size = 6778 },
]

[[package]]
name = "pyobjc-framework-modelio"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://files.pythonhosted.org/packages/a0/27/140bf75706332729de252cc4141e8c8afe16a0e9e5818b5a23155aa3473c/pyobjc_framework_modelio-11.1.tar.gz", hash = "sha256:fad0fa2c09d468ac7e49848e144f7bbce6826f2178b3120add8960a83e5bfcb7", size = 123203 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/18/84/5f223b82894777388ef1aa09579d9c044044877a72075213741c97adc901/pyobjc_framework_modelio-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:5d5e11389bde0852490b2a37896aaf9eb674b2a3586f2c572f9101cecb7bc576", size = 20172 },
    { url = "https://files.pythonhosted.org/packages/00/8b/7c8b93d99d2102800834011f58d6e5cbb56d24c112c2e45c4730b103e4a3/pyobjc_framework_modelio-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:34fabde55d28aa8a12dd4476ad40182513cf87ee2fa928043aa6702961de302b", size = 20182 },
    { url = "https://files.pythonhosted.org/packages/4d/c1/4d7830a8bd4e5b077e03e72eb8b92a336f689d5203228ecab9900d58d3c3/pyobjc_framework_modelio-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:327e1f3020001fd15bfbf4d4228581a8f64bd85872fd697b7c306343c11e25a6", size = 20408 },
    { url = "https://files.pythonhosted.org/packages/a1/14/a42462624d06c87034dce4cf40ded2ca6750a4d2e393607b5fb927a773b4/pyobjc_framework_modelio-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:214a4078950bc7b86a1ea70504ecf292cccebe6515c70023efdddaaa6423f455", size = 20209 },
    { url = "https://files.pythonhosted.org/packages/65/db/5c24390c08fd4f895e760cc2160137248ec0c2fa8fc12cb1bdfd93fbcfa8/pyobjc_framework_modelio-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:1b1393ddb315c0e8bed3f6ce4e4b355869a30c81ff79bda3ca3a201c0fd06dad", size = 20440 },
]

[[package]]
name = "pyobjc-framework-multipeerconnectivity"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/73/99/75bf6170e282d9e546b353b65af7859de8b1b27ddc431fc4afbf15423d01/pyobjc_framework_multipeerconnectivity-11.1.tar.gz", hash = "sha256:a3dacca5e6e2f1960dd2d1107d98399ff81ecf54a9852baa8ec8767dbfdbf54b", size = 26149 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/b4/fe/5c29c227f6ed81147ec6ec3e681fc680a7ffe0360f96901371435ea68570/pyobjc_framework_multipeerconnectivity-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:970031deb3dbf8da1fcb04e785d4bd2eeedae8f6677db92881df6d92b05c31d6", size = 11981 },
    { url = "https://files.pythonhosted.org/packages/d3/ea/f8d928235a67feeefec80e1f679bdb0c05f94e718a9aa22b4968ad65c6d1/pyobjc_framework_multipeerconnectivity-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:c92c95ea611d5272ab37fd73bc8e68c3d8fde515a75b97d8b22dafa8acbc7daf", size = 11992 },
    { url = "https://files.pythonhosted.org/packages/5a/ff/e60c8681d5c916f68fc78276d9243a91efc94a0e98717b535ce0b16e9db0/pyobjc_framework_multipeerconnectivity-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:296e10d289887cc4141c660f884cced1ec4ce64a19b3e406f13f6ce453a9425f", size = 12172 },
    { url = "https://files.pythonhosted.org/packages/a9/e3/2d5cea88ac0dc4ac0b2669fa43019fcdc701463c1f08e15fc5446a6dbd2a/pyobjc_framework_multipeerconnectivity-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:35c1a4a4b16df68b658b8531f97799995816a5bf49efd66805e3057b9bb9e474", size = 11980 },
    { url = "https://files.pythonhosted.org/packages/c3/84/154fe3919bf085575e9bc7b617b31914f4f4238d1b3cf0a5c75a7bfff911/pyobjc_framework_multipeerconnectivity-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:c28ad5c0c6d28cbc897aaebcc5f14798762aa9fec7f9110171570fef4d8d8a36", size = 12157 },
]

[[package]]
name = "pyobjc-framework-naturallanguage"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/a2/e9/5352fbf09c5d5360405dea49fb77e53ed55acd572a94ce9a0d05f64d2b70/pyobjc_framework_naturallanguage-11.1.tar.gz", hash = "sha256:ab1fc711713aa29c32719774fc623bf2d32168aed21883970d4896e901ff4b41", size = 46120 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/4b/f2/de86665d48737c74756b016c0f3bf93c99ca4151b48b14e2fbe7233283f8/pyobjc_framework_naturallanguage-11.1-py2.py3-none-any.whl", hash = "sha256:65a780273d2cdd12a3fa304e9c9ad822cb71facd9281f1b35a71640c53826f7c", size = 5306 },
]

[[package]]
name = "pyobjc-framework-netfs"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/68/5d/d68cc59a1c1ea61f227ed58e7b185a444d560655320b53ced155076f5b78/pyobjc_framework_netfs-11.1.tar.gz", hash = "sha256:9c49f050c8171dc37e54d05dd12a63979c8b6b565c10f05092923a2250446f50", size = 15910 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/77/cc/199b06f214f8a2db26eb47e3ab7015a306597a1bca25dcb4d14ddc65bd4a/pyobjc_framework_netfs-11.1-py2.py3-none-any.whl", hash = "sha256:f202e8e0c2e73516d3eac7a43b1c66f9911cdbb37ea32750ed197d82162c994a", size = 4143 },
]

[[package]]
name = "pyobjc-framework-network"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/0a/ee/5ea93e48eca341b274027e1532bd8629fd55d609cd9c39c2c3acf26158c3/pyobjc_framework_network-11.1.tar.gz", hash = "sha256:f6df7a58a1279bbc976fd7e2efe813afbbb18427df40463e6e2ee28fba07d2df", size = 124670 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/15/c2/3c6626fdb3616fde2c173d313d15caea22d141abcc2fbf3b615f8555abe3/pyobjc_framework_network-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:8cdc9be8ec3b0ae95e5c649e4bbcdf502cffd357dacc566223be707bdd5ac271", size = 19513 },
    { url = "https://files.pythonhosted.org/packages/91/96/0824455bab6d321ccb5a38907ab8593e1c83b283ec850abee494278f1c96/pyobjc_framework_network-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:04582fef567392c2a10dcee9519356b79b17ab73ded050d14592da938d95b01a", size = 19537 },
    { url = "https://files.pythonhosted.org/packages/5d/77/a088cfef5daf5841274b49fc57f5c5f70954c4a60b9a26160cb7beeb3e3a/pyobjc_framework_network-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:acf16738ab447a31a9f6167171b2a00d65a9370a8e84482d435b2b31c58eed94", size = 19600 },
    { url = "https://files.pythonhosted.org/packages/58/af/a5a22f53f0b31c584d39ddda0d3c55f41ffdbaec95a130f86fbc2e52cd0f/pyobjc_framework_network-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:cafdf953aa80934d30726baa681c1af61daf2cc9fe9e3ca582f4e3796bd0d053", size = 14769 },
    { url = "https://files.pythonhosted.org/packages/e6/cf/3cbbc1213caa45171fb2c8890a91302cee452283cc0be8b06aca35e2b1ad/pyobjc_framework_network-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:2e45d8fdc0ad553cc35839cae5eab221fe5f7ce28758d693b8159e619ea06eac", size = 14832 },
]

[[package]]
name = "pyobjc-framework-networkextension"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/71/30/d1eee738d702bbca78effdaa346a2b05359ab8a96d961b7cb44838e236ca/pyobjc_framework_networkextension-11.1.tar.gz", hash = "sha256:2b74b430ca651293e5aa90a1e7571b200d0acbf42803af87306ac8a1c70b0d4b", size = 217252 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/b6/26/526cd9f63e390e9c2153c41dc0982231b0b1ca88865deb538b77e1c3513d/pyobjc_framework_networkextension-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:853458aae8b43634461f6c44759750e2dc784c9aba561f9468ab14529b5a7fbe", size = 14114 },
    { url = "https://files.pythonhosted.org/packages/06/30/ab050541fda285e2ce6b6ba0f1f5215809bd5ec75f71de8057ff8135737a/pyobjc_framework_networkextension-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:d3d6e9810cb01c3a8f99aed5ee2d75f6f785204338b99b32e5f64370a18cc9dd", size = 14128 },
    { url = "https://files.pythonhosted.org/packages/07/36/3980a3ee5fe4be7c442cb4ddcf03f63406055da3f5ad58640fb573ecd77c/pyobjc_framework_networkextension-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:7dea914e7b26e28c6e4f8ffd03dd8fce612d38876043944fb0cf191774634566", size = 14275 },
    { url = "https://files.pythonhosted.org/packages/42/48/732767e8f858bd35fafce7ef846444569fb239e08d598e394c429c8bb78e/pyobjc_framework_networkextension-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:4c9d6c08b8f1cf374351bcecf8bbc91e6a8999b84d52f30964f4f1e6a323943c", size = 14179 },
    { url = "https://files.pythonhosted.org/packages/c8/02/9b2493f6894c873c751e097b692744ce0360248ff1b55dd64ff3716877d6/pyobjc_framework_networkextension-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:6d730540d97662867f3cfd90c9a1e69a6adae0f5eb554c1b94a1b067e7ebc728", size = 14323 },
]

[[package]]
name = "pyobjc-framework-notificationcenter"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/a8/4a/d3529b9bd7aae2c89d258ebc234673c5435e217a5136abd8c0aba37b916b/pyobjc_framework_notificationcenter-11.1.tar.gz", hash = "sha256:0b938053f2d6b1cea9db79313639d7eb9ddd5b2a5436a346be0887e75101e717", size = 23389 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/6d/92/cd00fe5e54a191fb77611fe728a8c8a0a6edb229857d32f27806582406ca/pyobjc_framework_notificationcenter-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:65fc67374a471890245c7a1d60cf67dcf160075a9c048a5d89608a8290f33b03", size = 9880 },
    { url = "https://files.pythonhosted.org/packages/40/e4/1bc444c5ee828a042e951c264ce597207e192fb6701c380db5ba05486955/pyobjc_framework_notificationcenter-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:f5ce98882e301adef07651ba495ddd57b661d4c0398afd39f4591c1b44673cca", size = 9895 },
    { url = "https://files.pythonhosted.org/packages/13/b9/b98d74bcc9e1694494b81dd1bfeb28e2f004041db4945b7451c0c6c64b1e/pyobjc_framework_notificationcenter-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:e46285290d04e84c167606ccfcb9a20c2567f5a2a6a9c6e96760fc9d561c2740", size = 10090 },
    { url = "https://files.pythonhosted.org/packages/4b/1e/3d6b9765f3f2719733b099cb48750366d9bbd431a1b5b0e6dd30ece7a995/pyobjc_framework_notificationcenter-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:c3e79e9c57f130099b47bde48f26fcd90ab3b52e01d989ea15b7cdb7fa5a34d8", size = 9935 },
    { url = "https://files.pythonhosted.org/packages/f3/13/1a85878f14232d8b7012a5a24dbf185dec1864dc92ca53db4c62390b6ee5/pyobjc_framework_notificationcenter-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:15e49491d7f091eaa643f2fd89787becbf767dd6c609aa3d01e53132cb1d9fa1", size = 10137 },
]

[[package]]
name = "pyobjc-framework-opendirectory"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/9d/02/ac56c56fdfbc24cdf87f4a624f81bbe2e371d0983529b211a18c6170e932/pyobjc_framework_opendirectory-11.1.tar.gz", hash = "sha256:319ac3424ed0350be458b78148914468a8fc13a069d62e7869e3079108e4f118", size = 188880 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/06/56/f0f5b7222d5030192c44010ab7260681e349efea2f1b1b9f116ba1951d6d/pyobjc_framework_opendirectory-11.1-py2.py3-none-any.whl", hash = "sha256:bb4219b0d98dff4a952c50a79b1855ce74e1defd0d241f3013def5b09256fd7b", size = 11829 },
]

[[package]]
name = "pyobjc-framework-osakit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/56/22/f9cdfb5de255b335f99e61a3284be7cb1552a43ed1dfe7c22cc868c23819/pyobjc_framework_osakit-11.1.tar.gz", hash = "sha256:920987da78b67578367c315d208f87e8fab01dd35825d72242909f29fb43c820", size = 22290 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/14/65/c6531ce0792d5035d87f054b0ccf22e453328fda2e68e11a7f70486da23a/pyobjc_framework_osakit-11.1-py2.py3-none-any.whl", hash = "sha256:1b0c0cc537ffb8a8365ef9a8b46f717a7cc2906414b6a3983777a6c0e4d53d5a", size = 4143 },
]

[[package]]
name = "pyobjc-framework-oslog"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-coremedia" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://files.pythonhosted.org/packages/79/93/3feb7f6150b50165524750a424f5434448392123420cb4673db766c3f54a/pyobjc_framework_oslog-11.1.tar.gz", hash = "sha256:b2af409617e6b68fa1f1467c5a5679ebf59afd0cdc4b4528e1616059959a7979", size = 24689 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/40/da/fd3bd62899cd679743056aa2c28bc821c2688682a17ddde1a08d6d9d67fc/pyobjc_framework_oslog-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:7ae29c31ce51c476d3a37ca303465dd8bdfa98df2f6f951cf14c497e984a1ba9", size = 7799 },
    { url = "https://files.pythonhosted.org/packages/9d/a9/d26bb3ec7ab2a3ef843c1697b6084dbd4a4a98d90ff8e29f4c227ade425e/pyobjc_framework_oslog-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:7174ca2cdc073e555d5f5aea3baa7410c61a83a3741eaec23e8581340037680e", size = 7811 },
    { url = "https://files.pythonhosted.org/packages/44/60/2f57ee052e9df2700b21032774146ae622af0a88a8dff97158dc5850a0ec/pyobjc_framework_oslog-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:f03789f8d5638e1075652b331b8ebf98c03dfa809c57545f0313583a7688bb86", size = 7995 },
    { url = "https://files.pythonhosted.org/packages/2f/f1/13fe8d1cebe29953e8754d9118399805b266e17ef885f628f62f2d2deb9b/pyobjc_framework_oslog-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:a302272aa40d1655be635e0f0dd0ca71b5fce562dfcb88a87165a170a648b2fd", size = 7847 },
    { url = "https://files.pythonhosted.org/packages/37/82/a5a2fb3333c3f55ba696baee67668e44380b9838dd91b64a038ed57cee41/pyobjc_framework_oslog-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:cade8869e185a29fb88fc48e2e5c984548433f669c1a40ec7f5640994fa36603", size = 8034 },
]

[[package]]
name = "pyobjc-framework-passkit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/5c/05/063db500e7df70e39cbb5518a5a03c2acc06a1ca90b057061daea00129f3/pyobjc_framework_passkit-11.1.tar.gz", hash = "sha256:d2408b58960fca66607b483353c1ffbd751ef0bef394a1853ec414a34029566f", size = 144859 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/9d/ba/9e52213e0c0100079e4ef397cf4fd5ba8939fa4de19339755d1a373407a8/pyobjc_framework_passkit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:779eaea4e1931cfda4c8701e1111307b14bf9067b359a319fc992b6848a86932", size = 13959 },
    { url = "https://files.pythonhosted.org/packages/d1/4f/e29dc665382e22cd6b4ebb1c5707a1b2059018a6462c81a7c344a9c40dba/pyobjc_framework_passkit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:a6306dda724ca812dca70154d40f32ec9bbdaff765a12f3cc45391723efe147e", size = 13971 },
    { url = "https://files.pythonhosted.org/packages/f4/ec/ef03f62924b288302e41373c4c292cadf4c393519828a9986d8573b72bcc/pyobjc_framework_passkit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:d7948d5b3369b60808a85dcadffdebb0a44e8d2c4716edc10b78cb76fa762070", size = 14130 },
    { url = "https://files.pythonhosted.org/packages/92/cb/4ecaf64825de3589cbf5119cf6bfabe7b466faff58357800255c2ecf41e1/pyobjc_framework_passkit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:bfff2a63850afe702ba25f661360393389ffb58e127d47488c414caa9e676aa7", size = 14010 },
    { url = "https://files.pythonhosted.org/packages/ce/72/125088bd20a8f771cc1749c6be786241839c6bdb6a581cf025663f55fa1f/pyobjc_framework_passkit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:f6b7f3cd7c6855af1b6fc4036ae2f10779a312182107c94d36ef63c2dd4a6f87", size = 14180 },
]

[[package]]
name = "pyobjc-framework-pencilkit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/75/d0/bbbe9dadcfc37e33a63d43b381a8d9a64eca27559df38efb74d524fa6260/pyobjc_framework_pencilkit-11.1.tar.gz", hash = "sha256:9c173e0fe70179feadc3558de113a8baad61b584fe70789b263af202bfa4c6be", size = 22570 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/a3/f6/59ffc3f26ea9cfda4d40409f9afc2a38e5c0c6a68a3a8c9202e8b98b03b1/pyobjc_framework_pencilkit-11.1-py2.py3-none-any.whl", hash = "sha256:b7824907bbcf28812f588dda730e78f662313baf40befd485c6f2fcb49018019", size = 4026 },
]

[[package]]
name = "pyobjc-framework-phase"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-avfoundation" },
]
sdist = { url = "https://files.pythonhosted.org/packages/c6/d2/e9384b5b3fbcc79e8176cb39fcdd48b77f60cd1cb64f9ee4353762b037dc/pyobjc_framework_phase-11.1.tar.gz", hash = "sha256:a940d81ac5c393ae3da94144cf40af33932e0a9731244e2cfd5c9c8eb851e3fc", size = 58986 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/f5/9e/55782f02b3bfb58f030b062176e8b0dba5f8fbd6e50d27a687f559c4179d/pyobjc_framework_phase-11.1-py2.py3-none-any.whl", hash = "sha256:cfa61f9c6c004161913946501538258aed48c448b886adbf9ed035957d93fa15", size = 6822 },
]

[[package]]
name = "pyobjc-framework-photos"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/78/b0/576652ecd05c26026ab4e75e0d81466edd570d060ce7df3d6bd812eb90d0/pyobjc_framework_photos-11.1.tar.gz", hash = "sha256:c8c3b25b14a2305047f72c7c081ff3655b3d051f7ed531476c03246798f8156d", size = 92569 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/fa/24/2400e6b738d3ed622c61a7cc6604eec769f398071a1eb6a16dfdf3a9ceea/pyobjc_framework_photos-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:8dbfffd29cfa63a8396ede0030785c15a5bc36065d3dd98fc6176a59e7abb3d3", size = 12224 },
    { url = "https://files.pythonhosted.org/packages/70/60/cc575ee4287b250a42406e9b335f3293840996a840152cf93d1ce73790c5/pyobjc_framework_photos-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:541d8fafdb2f111f2f298e1aa0542f2d5871ce1dd481c3e9be4ed33916b38c3a", size = 12241 },
    { url = "https://files.pythonhosted.org/packages/8c/3b/d9c4c5b156e7805495a8864dd06a3439c3b4267e5887d9094ac45a4ca907/pyobjc_framework_photos-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:7cded282eaebd77645a4262f6fb63379c7a226d20f8f1763910b19927709aea2", size = 12426 },
    { url = "https://files.pythonhosted.org/packages/28/86/06d9e61aa5c6114cca5ae77e3c037f371943e9110aab4ce6d31d19ffb669/pyobjc_framework_photos-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:3a759ebcf46493cd09e5c89c0a09096ad83ae837d9236e437571bb22ca6eab3f", size = 12290 },
    { url = "https://files.pythonhosted.org/packages/69/07/849ca5aefc646b92ea399073f90628215198701a59c1b62b7bf3e27bbbdf/pyobjc_framework_photos-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:72e0ed9bc5f1890f882df55333797da95c0ed1c1d7a0fe7d869a8d4ee4e1bdfd", size = 12470 },
]

[[package]]
name = "pyobjc-framework-photosui"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/20/bb/e6de720efde2e9718677c95c6ae3f97047be437cda7a0f050cd1d6d2a434/pyobjc_framework_photosui-11.1.tar.gz", hash = "sha256:1c7ffab4860ce3e2b50feeed4f1d84488a9e38546db0bec09484d8d141c650df", size = 48443 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/f8/c1/a5c84c1695e7a066743d63d10b219d94f3c07d706871682e42f7db389f5c/pyobjc_framework_photosui-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:b2f278f569dfd596a32468351411518a651d12cb91e60620094e852c525a5f10", size = 11682 },
    { url = "https://files.pythonhosted.org/packages/33/10/506af430a9e7d356302b6bbee6672e03a4dfbc9a2f3a90fa79607d06387d/pyobjc_framework_photosui-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:6f0fa9c9e363c0db54957dfe4e26214379f2698caaba1e4ff4c9e3eba5e690d9", size = 11697 },
    { url = "https://files.pythonhosted.org/packages/9f/f8/ada0d54136f14b071e784e7f86e0a1e2190e2e898a7f4172b53e1fec5f7c/pyobjc_framework_photosui-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:91aff7caae16a7a7f25e35692aa92b796155510b8a0575668e75f351fbf63a68", size = 11894 },
    { url = "https://files.pythonhosted.org/packages/1b/7d/b55a787f90e29f36b776cf87b9515a53014449d9cddd109b9e81c9e9d7eb/pyobjc_framework_photosui-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:e607242e09fb7d4bcad2f3eb2e88529d8f2ff7cf7341cd2c6c5b3f4d6744218e", size = 11670 },
    { url = "https://files.pythonhosted.org/packages/07/be/3e98e69e513b3948080ede2a13b0f73f081db50c716519fcee4a932de0b6/pyobjc_framework_photosui-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:f11f6043c83b2c65ecad69c48844fff6368127af3956ec8df9726bbd1e5da17e", size = 11891 },
]

[[package]]
name = "pyobjc-framework-preferencepanes"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/34/ac/9324602daf9916308ebf1935b8a4b91c93b9ae993dcd0da731c0619c2836/pyobjc_framework_preferencepanes-11.1.tar.gz", hash = "sha256:6e4a55195ec9fc921e0eaad6b3038d0ab91f0bb2f39206aa6fccd24b14a0f1d8", size = 26212 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/a1/51/75c7e32272241f706ce8168e04a32be02c4b0c244358330f730fc85695c3/pyobjc_framework_preferencepanes-11.1-py2.py3-none-any.whl", hash = "sha256:6ee5f5a7eb294e03ea3bac522ac4b69e6dc83ceceff627a0a2d289afe1e01ad9", size = 4786 },
]

[[package]]
name = "pyobjc-framework-pushkit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/9f/f0/92d0eb26bf8af8ebf6b5b88df77e70b807de11f01af0162e0a429fcfb892/pyobjc_framework_pushkit-11.1.tar.gz", hash = "sha256:540769a4aadc3c9f08beca8496fe305372501eb28fdbca078db904a07b8e10f4", size = 21362 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/31/65/260014c5d13c54bd359221b0a890cbffdb99eecff3703f253cf648e45036/pyobjc_framework_pushkit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:21993b7e9127b05575a954faa68e85301c6a4c04e34e38aff9050f67a05c562a", size = 8174 },
    { url = "https://files.pythonhosted.org/packages/b4/b2/08514fa6be83a359bb6d72f9009f17f16f7efc0fe802029d1f6f0c4fc5c9/pyobjc_framework_pushkit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:bac3ee77dfbe936998f207c1579e346993485bab8849db537ed250261cf12ab3", size = 8190 },
    { url = "https://files.pythonhosted.org/packages/46/d0/cbe99c9bf3b9fb2679c08f4051aaa44dcfbfa9e762f0ef4c7fc5ad2e147e/pyobjc_framework_pushkit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:68c4f44354eab84cb54d43310fa65ca3a5ba68299c868378764cc50803cf2adc", size = 8314 },
    { url = "https://files.pythonhosted.org/packages/87/ff/7b0747471b837580dc01709438a5a0949ce909957d2857408bd81bf22155/pyobjc_framework_pushkit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:cfec36cdca24654be0465282eb31b7ff3674ea4b7f3ce696b07edbe33b000aa5", size = 8240 },
    { url = "https://files.pythonhosted.org/packages/86/96/422875f53390579dd51d1cdc696290c5693d293e9c4cb0f6d4e7a0905f88/pyobjc_framework_pushkit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:80d5d8240b71631d81cfa96f398fae1d137be98f224739e50edaf9e5afc21a9d", size = 8368 },
]

[[package]]
name = "pyobjc-framework-quartz"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/c7/ac/6308fec6c9ffeda9942fef72724f4094c6df4933560f512e63eac37ebd30/pyobjc_framework_quartz-11.1.tar.gz", hash = "sha256:a57f35ccfc22ad48c87c5932818e583777ff7276605fef6afad0ac0741169f75", size = 3953275 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/9b/37/ee6e0bdd31b3b277fec00e5ee84d30eb1b5b8b0e025095e24ddc561697d0/pyobjc_framework_quartz-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:9ac806067541917d6119b98d90390a6944e7d9bd737f5c0a79884202327c9204", size = 216410 },
    { url = "https://files.pythonhosted.org/packages/bd/27/4f4fc0e6a0652318c2844608dd7c41e49ba6006ee5fb60c7ae417c338357/pyobjc_framework_quartz-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:43a1138280571bbf44df27a7eef519184b5c4183a588598ebaaeb887b9e73e76", size = 216816 },
    { url = "https://files.pythonhosted.org/packages/b8/8a/1d15e42496bef31246f7401aad1ebf0f9e11566ce0de41c18431715aafbc/pyobjc_framework_quartz-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:b23d81c30c564adf6336e00b357f355b35aad10075dd7e837cfd52a9912863e5", size = 221941 },
    { url = "https://files.pythonhosted.org/packages/32/a8/a3f84d06e567efc12c104799c7fd015f9bea272a75f799eda8b79e8163c6/pyobjc_framework_quartz-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:07cbda78b4a8fcf3a2d96e047a2ff01f44e3e1820f46f0f4b3b6d77ff6ece07c", size = 221312 },
    { url = "https://files.pythonhosted.org/packages/76/ef/8c08d4f255bb3efe8806609d1f0b1ddd29684ab0f9ffb5e26d3ad7957b29/pyobjc_framework_quartz-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:39d02a3df4b5e3eee1e0da0fb150259476910d2a9aa638ab94153c24317a9561", size = 226353 },
]

[[package]]
name = "pyobjc-framework-quicklookthumbnailing"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://files.pythonhosted.org/packages/aa/98/6e87f360c2dfc870ae7870b8a25fdea8ddf1d62092c755686cebe7ec1a07/pyobjc_framework_quicklookthumbnailing-11.1.tar.gz", hash = "sha256:1614dc108c1d45bbf899ea84b8691288a5b1d25f2d6f0c57dfffa962b7a478c3", size = 16527 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/65/4a/ddc35bdcd44278f22df2154a52025915dba6c80d94e458d92e9e7430d1e4/pyobjc_framework_quicklookthumbnailing-11.1-py2.py3-none-any.whl", hash = "sha256:4d1863c6c83c2a199c1dbe704b4f8b71287168f4090ed218d37dc59277f0d9c9", size = 4219 },
]

[[package]]
name = "pyobjc-framework-replaykit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/c8/4f/014e95f0fd6842d7fcc3d443feb6ee65ac69d06c66ffa9327fc33ceb7c27/pyobjc_framework_replaykit-11.1.tar.gz", hash = "sha256:6919baa123a6d8aad769769fcff87369e13ee7bae11b955a8185a406a651061b", size = 26132 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/b9/73/846cebb36fc279df18f10dc3a27cba8fe2e47e95350a3651147e4d454719/pyobjc_framework_replaykit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:22c6d09be9a6e758426d723a6c3658ad6bbb66f97ba9a1909bfcf29a91d99921", size = 10087 },
    { url = "https://files.pythonhosted.org/packages/bf/2e/996764cd045b6c9e033167e573c9fe67c4e867eb6ab49c2d4fde005cd4a7/pyobjc_framework_replaykit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:7742ee18c8c9b61f5668698a05b88d25d34461fcdd95a8f669ecdfd8db8c4d42", size = 10108 },
    { url = "https://files.pythonhosted.org/packages/d6/f9/1013a88f655b9eaf6fc81a5da48403724435cf2f87c147038dfa733e6213/pyobjc_framework_replaykit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:b503fabc33ee02117fd82c78db18cba3f0be90dea652f5553101a45185100402", size = 10298 },
    { url = "https://files.pythonhosted.org/packages/fc/df/62a735c034bdbd0670f93636725b898a762fd23532a3841ae491bc8d16bd/pyobjc_framework_replaykit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:da84e48ba5d529ae72b975f0d81c5bd5427983c2b05d3d2c7fd54a6cbdf0d0f9", size = 10170 },
    { url = "https://files.pythonhosted.org/packages/56/00/d582fd058e580e5f803ee57fa8513b7df0c6d2abca876e04a4bc682b7143/pyobjc_framework_replaykit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:2bf2180feae500fdd6f14360200fda0b6650a4ec39fe5d84a5dde9e8cdd307b6", size = 10347 },
]

[[package]]
name = "pyobjc-framework-safariservices"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/1a/fc/c47d2abf3c1de6db21d685cace76a0931d594aa369e3d090260295273f6e/pyobjc_framework_safariservices-11.1.tar.gz", hash = "sha256:39a17df1a8e1c339457f3acbff0dc0eae4681d158f9d783a11995cf484aa9cd0", size = 34905 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/d7/13/9636e9d3dc362daaaa025b2aa4e28606a1e197dfc6506d3a246be8315f8a/pyobjc_framework_safariservices-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:c92eb9e35f98368ea1bfaa8cdd41138ca8b004ea5a85833390a44e5626ca5061", size = 7275 },
    { url = "https://files.pythonhosted.org/packages/de/cd/9ed0083373be3bf6da2450a6800b54965fea95b2452473ee0e36ddc72573/pyobjc_framework_safariservices-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:8b4d4169dd21e69246d90a42f872b7148064b63de6bbbf6bc6ddabe33f143843", size = 7290 },
    { url = "https://files.pythonhosted.org/packages/42/ed/3eaec77c81395410441466f66c8920664ba72f62099306f0e9b878b0b203/pyobjc_framework_safariservices-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:8a4371d64052a3ffe9993a89c45f9731f86e7b6c21fd1d968815fd7930ff501a", size = 7293 },
    { url = "https://files.pythonhosted.org/packages/d2/5f/5bbdf64ec7ff2c1d90e0b7b7186a55981632c16ce757b3187e87d6707c7e/pyobjc_framework_safariservices-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:abdbe0d8a79caa994a1d2be8ea4e5a1e4c80f7d8e1f0750f9c365129d1f1a968", size = 7312 },
    { url = "https://files.pythonhosted.org/packages/fd/2a/dd6d53915c83c1e68bd8cfdec5cf71c4b3c6e1b7c737353f109b2dde5426/pyobjc_framework_safariservices-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:8a6ec417d35a0600629eba97c0ab2f2d09fae171e8bca3d3d6aa1c7ff272c4d7", size = 7318 },
]

[[package]]
name = "pyobjc-framework-safetykit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://files.pythonhosted.org/packages/28/cc/f6aa5d6f45179bd084416511be4e5b0dd0752cb76daa93869e6edb806096/pyobjc_framework_safetykit-11.1.tar.gz", hash = "sha256:c6b44e0cf69e27584ac3ef3d8b771d19a7c2ccd9c6de4138d091358e036322d4", size = 21240 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/9c/8f/6f4c833e31526a81faef9bf19695b332ba8d2fa53d92640abd6fb3ac1d78/pyobjc_framework_safetykit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:b76fccdb970d3d751a540c47712e9110afac9abea952cb9b7bc0d5867db896e3", size = 8523 },
    { url = "https://files.pythonhosted.org/packages/85/3d/782e1738f2eb4b276baabd85a8b263bf75b2c4e990fd5950eeadfb59ebeb/pyobjc_framework_safetykit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:8130de57f701dbccb1d84c76ec007fe04992da58cbf0eb906324393eeac3d08d", size = 8541 },
    { url = "https://files.pythonhosted.org/packages/be/2c/411d525a2110777dd22888e46a48dcff2ae15ff08ab2f739eab44ee740cb/pyobjc_framework_safetykit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:cd8091c902037eac4a403d8462424afd711f43206af3548a34bebe1f59d2c340", size = 8701 },
    { url = "https://files.pythonhosted.org/packages/ca/df/f04b5caa76b2e4c5115c55937b50c341963c35ded6931cb1a3bc0e686d0b/pyobjc_framework_safetykit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:761304365978d650015fe05fb624ba13ea4af6c6a76ef8e344673f5b0fed2e92", size = 8581 },
    { url = "https://files.pythonhosted.org/packages/a5/66/e0bd5ac4956e4f6d77815c85355764e43934a31c8fdd10e33b4ff217cb99/pyobjc_framework_safetykit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:24d5ce9dfb80abb634a95ceda3da0f0cdb52c765db0f47de953a4f66b918c957", size = 8746 },
]

[[package]]
name = "pyobjc-framework-scenekit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://files.pythonhosted.org/packages/64/cf/2d89777120d2812e7ee53c703bf6fc8968606c29ddc1351bc63f0a2a5692/pyobjc_framework_scenekit-11.1.tar.gz", hash = "sha256:82941f1e5040114d6e2c9fd35507244e102ef561c637686091b71a7ad0f31306", size = 214118 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/e0/f9/bdcd8a4bc6c387ef07f3e2190cea6a03d4f7ed761784f492b01323e8d900/pyobjc_framework_scenekit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:c803d95b30c4ce49f46ff7174806f5eb84e4c3a152f8f580c5da0313c5c67041", size = 33558 },
    { url = "https://files.pythonhosted.org/packages/ce/5e/9bb308fd68b56a8cf9ea5213e6c988232ce6ae4e6ccd4cf53b38f0018deb/pyobjc_framework_scenekit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:2f347d5ae42af8acddb86a45f965046bb91f8d83d33851390954439961e2a7b7", size = 33577 },
    { url = "https://files.pythonhosted.org/packages/e0/96/c960c553de8e70f0bff275e19295b6254127f3f6d1da4e5dd80fd7037d49/pyobjc_framework_scenekit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:ea2f02eea982872994d7c366f6a51060a90cc17b994c017f85c094e2bc346847", size = 33912 },
    { url = "https://files.pythonhosted.org/packages/04/29/c342990cc245a3bdbb9d55807ce8009575acb705dbce24164001850ec41e/pyobjc_framework_scenekit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:2be143172b43c2cf4a2b3fad9e15ffb5d29df677d3678160cd125b94a30caaca", size = 34061 },
    { url = "https://files.pythonhosted.org/packages/25/aa/eff356d201d32b1f7e2a2e8c6629899cb31bcc33933816055ce1b90df31a/pyobjc_framework_scenekit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:3f62f2b8f26375ecfec71f7fdb23f2739cf93d213968c6ffac6a8525516ffc6e", size = 34365 },
]

[[package]]
name = "pyobjc-framework-screencapturekit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-coremedia" },
]
sdist = { url = "https://files.pythonhosted.org/packages/32/a5/9bd1f1ad1773a1304ccde934ff39e0f0a0b0034441bf89166aea649606de/pyobjc_framework_screencapturekit-11.1.tar.gz", hash = "sha256:11443781a30ed446f2d892c9e6642ca4897eb45f1a1411136ca584997fa739e0", size = 53548 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/98/37/840f306dcf01dd2bd092ae8dcf371a3bad3a0f88f0780d0840f899a8c047/pyobjc_framework_screencapturekit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:641fa7834f54558859209e174c83551d5fa239ca6943ace52665f7d45e562ff2", size = 11308 },
    { url = "https://files.pythonhosted.org/packages/1b/9e/de4c2e3ae834c2f60c9e78d95e1f2488b679b4cf74fa5bfba7f065fb827b/pyobjc_framework_screencapturekit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:1119d6258d6c668564ab39154cfc745fd2bb8b3beeaa4f9b2a8a4c93926678c0", size = 11324 },
    { url = "https://files.pythonhosted.org/packages/4c/49/fa1680b8453fb5c4bbe92b2bfef145fd90b3cd9c2ee24c1eb786b7655cd3/pyobjc_framework_screencapturekit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:f93f8198741bd904d423a7b1ef941445246bdf6cb119597d981e61a13cc479a4", size = 11517 },
    { url = "https://files.pythonhosted.org/packages/12/cd/035192d486f4323d0d891b50fd2229a58e80fd341e19fa7ae9d71c38c8e2/pyobjc_framework_screencapturekit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:9e135b414d3829fcf7fd8a66c94e8b51135fb9f630c10488fb9d78f27f622906", size = 11396 },
    { url = "https://files.pythonhosted.org/packages/a3/4a/e2752b1d91ce420ccd58a24e5e819230007fa50e97719a78857a76f8ab6d/pyobjc_framework_screencapturekit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:9972db69064b69e78fbc6a00f1de2d8eaa225b990b23687970328b061e60e26d", size = 11578 },
]

[[package]]
name = "pyobjc-framework-screensaver"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/7c/f6/f2d48583b29fc67b64aa1f415fd51faf003d045cdb1f3acab039b9a3f59f/pyobjc_framework_screensaver-11.1.tar.gz", hash = "sha256:d5fbc9dc076cc574ead183d521840b56be0c160415e43cb8e01cfddd6d6372c2", size = 24302 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/76/f9/4ae982c7a1387b64954130b72187e140329b73c647acb4d6b6eb3c033d8d/pyobjc_framework_screensaver-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:f2d22293cf9d715e4692267a1678096afd6793c0519d9417cf77c8a6c706a543", size = 8402 },
    { url = "https://files.pythonhosted.org/packages/dc/ff/c2e83551474d3c401181ce1d859ebd0e0b1986ab8ee932d647debebbe7eb/pyobjc_framework_screensaver-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:46d65c1e14d35f287e7be351e2f98daf9489e31e7ca0d306e6102904ce6c40fb", size = 8419 },
    { url = "https://files.pythonhosted.org/packages/7a/b7/e633cd8e07bcfcd675155c7fd00f82cab0d09ca3edee0f568bcfc0ae8ea4/pyobjc_framework_screensaver-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:2c01a9646bc118445cbb117e7016bd1df9fe93a65db991ab5496d59b1a7bc66d", size = 8423 },
    { url = "https://files.pythonhosted.org/packages/65/55/ac2b76a86646b6f86163d1e06c2ca36f4b0fb168ae889ab3af657b724817/pyobjc_framework_screensaver-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:e32c83e1d9e5044d482916ac42257a87d1f1068f3f6bccaa04edda40fb9f9ad1", size = 8457 },
    { url = "https://files.pythonhosted.org/packages/d5/e7/494e6aa650c071abd3b44a0168123a174636a1fc9d198f0db80d642703cc/pyobjc_framework_screensaver-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:7852c2281148cb99c87c4c25b83dca7fdd11e6eed04deadcf2201ed5a2079e5f", size = 8462 },
]

[[package]]
name = "pyobjc-framework-screentime"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/82/33/ebed70a1de134de936bb9a12d5c76f24e1e335ff4964f9bb0af9b09607f1/pyobjc_framework_screentime-11.1.tar.gz", hash = "sha256:9bb8269456bbb674e1421182efe49f9168ceefd4e7c497047c7bf63e2f510a34", size = 14875 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/ea/20/783eccea7206ceeda42a09a4614e3da92889e4c54abe9dec2e5e53576e1a/pyobjc_framework_screentime-11.1-py2.py3-none-any.whl", hash = "sha256:50a4e4ab33d6643a52616e990aa1c697d5e3e8f9f9bdab8d631e6d42d8287b4f", size = 3949 },
]

[[package]]
name = "pyobjc-framework-scriptingbridge"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/8e/c1/5b1dd01ff173df4c6676f97405113458918819cb2064c1735b61948e8800/pyobjc_framework_scriptingbridge-11.1.tar.gz", hash = "sha256:604445c759210a35d86d3e0dfcde0aac8e5e3e9d9e35759e0723952138843699", size = 23155 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/c1/64/31849063e3e81b4c312ce838dc98f0409c09eb33bc79dbb5261cb994a4c4/pyobjc_framework_scriptingbridge-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:226ba12d9cbd504411b702323b0507dd1690e81b4ce657c5f0d8b998c46cf374", size = 8323 },
    { url = "https://files.pythonhosted.org/packages/d8/19/3003d4a137ce84fa8cb42a9c84f8c04e83c89749ab9cf93bc755016434b7/pyobjc_framework_scriptingbridge-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:c2ba0ad3d3e4e3c6a43fe3e84ab02c5c4e74000bb6f130ae47bf82a3dcd4af98", size = 8337 },
    { url = "https://files.pythonhosted.org/packages/e3/1c/0b90b4bcef7ea8fb80cb5f6fa0b73be075f2dffa2ba03580b37592dc8dad/pyobjc_framework_scriptingbridge-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:57f5401826e3a008d9cfb7c164187859cadc1b1f96194dc0a7c596f502548c26", size = 8485 },
    { url = "https://files.pythonhosted.org/packages/bc/9d/22238e06780630ae3ec26d6af17df87d649fca0d9879caeaaf4f36b147c1/pyobjc_framework_scriptingbridge-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:a84d0a8ff4fa1f0016f5d797ad93e22e437212a2fc8e6417a3b8d68f89229680", size = 8346 },
    { url = "https://files.pythonhosted.org/packages/07/e1/fc755423ffc3b28a4c2905c607e55cbed471edc025ec5c0849de4bea1230/pyobjc_framework_scriptingbridge-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:5381e9be1299e1134489e4d46662c649613214265b3b691264cfba0b083929f5", size = 8499 },
]

[[package]]
name = "pyobjc-framework-searchkit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-coreservices" },
]
sdist = { url = "https://files.pythonhosted.org/packages/6e/20/61b73fddae0d1a94f5defb0cd4b4f391ec03bfcce7ebe830cb827d5e208a/pyobjc_framework_searchkit-11.1.tar.gz", hash = "sha256:13a194eefcf1359ce9972cd92f2aadddf103f3efb1b18fd578ba5367dff3c10c", size = 30918 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/2b/ed/a118d275a9132c8f5adcd353e4d9e844777068e33d51b195f46671161a7f/pyobjc_framework_searchkit-11.1-py2.py3-none-any.whl", hash = "sha256:9c9d6ca71cef637ccc3627225fb924a460b3d0618ed79bb0b3c12fcbe9270323", size = 3714 },
]

[[package]]
name = "pyobjc-framework-security"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/ee/6f/ba50ed2d9c1192c67590a7cfefa44fc5f85c776d1e25beb224dec32081f6/pyobjc_framework_security-11.1.tar.gz", hash = "sha256:dabcee6987c6bae575e2d1ef0fcbe437678c4f49f1c25a4b131a5e960f31a2da", size = 302291 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/35/16/7fc52ab1364ada5885bf9b4c9ea9da3ad892b847c9b86aa59e086b16fc11/pyobjc_framework_security-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:2eb4ba6d8b221b9ad5d010e026247e8aa26ee43dcaf327e848340ed227d22d7e", size = 41222 },
    { url = "https://files.pythonhosted.org/packages/3f/d8/cb20b4c4d15b2bdc7e39481159e50a933ddb87e4702d35060c254b316055/pyobjc_framework_security-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:158da3b2474e2567fd269531c4ee9f35b8ba4f1eccbd1fb4a37c85a18bf1243c", size = 41221 },
    { url = "https://files.pythonhosted.org/packages/cb/3c/d13d6870f5d66f5379565887b332f86f16d666dc50a1944d7e3a1462e76c/pyobjc_framework_security-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:141cc3ee08627ae0698264efc3dbbaf28d2255e0fe690e336eb8f0f387c4af01", size = 42099 },
    { url = "https://files.pythonhosted.org/packages/f0/3d/2f61d4566e80f203d0e05ddd788037dc06a94d200edac25d2747fd79b5aa/pyobjc_framework_security-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:858a18303711eb69d18d1a64cf8bb2202f64a3bd1c82203c511990dbd8326514", size = 41288 },
    { url = "https://files.pythonhosted.org/packages/15/44/99ef33a5319ed2cb6c0a51ed36214adf21ccb37cce970b1acc8bfe57ce23/pyobjc_framework_security-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:4db1ebf6395cd370139cb35ff172505fc449c7fdf5d3a28f2ada8a30ef132cd0", size = 42849 },
]

[[package]]
name = "pyobjc-framework-securityfoundation"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-security" },
]
sdist = { url = "https://files.pythonhosted.org/packages/5c/d4/19591dd0938a45b6d8711ef9ae5375b87c37a55b45d79c52d6f83a8d991f/pyobjc_framework_securityfoundation-11.1.tar.gz", hash = "sha256:b3c4cf70735a93e9df40f3a14478143959c415778f27be8c0dc9ae0c5b696b92", size = 13270 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/6c/ab/23db6b1c09810d6bcc4eab96e62487fb4284b57e447eabe6c001cb41e36d/pyobjc_framework_securityfoundation-11.1-py2.py3-none-any.whl", hash = "sha256:25f2cf10f80c122f462e9d4d43efe9fd697299c194e0c357e76650e234e6d286", size = 3772 },
]

[[package]]
name = "pyobjc-framework-securityinterface"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-security" },
]
sdist = { url = "https://files.pythonhosted.org/packages/a1/be/c846651c3e7f38a637c40ae1bcda9f14237c2395637c3a188df4f733c727/pyobjc_framework_securityinterface-11.1.tar.gz", hash = "sha256:e7aa6373e525f3ae05d71276e821a6348c53fec9f812b90eec1dbadfcb507bc9", size = 37648 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/6f/ab/48b8027a24f3f8924f5be5f97217961b4ed23e6be49b3bd94ee8a0d56a1e/pyobjc_framework_securityinterface-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:26056441b325029da06a7c7b8dd1a0c9a4ad7d980596c1b04d132a502b4cacc0", size = 10837 },
    { url = "https://files.pythonhosted.org/packages/31/2e/de226a3caa47b4a800c8e6289b9fe30c71f10985dbc37379d5bd0781b470/pyobjc_framework_securityinterface-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:708dd1d65309f3d4043ecaf152591c240601a5d3da7ae7a500f511c54317537b", size = 10851 },
    { url = "https://files.pythonhosted.org/packages/2a/9f/2d0c41ded78f9dc1e58d63b9d7ed55666b0d0d6ec78ce8938c7c4accdf59/pyobjc_framework_securityinterface-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:e9ebfb32177eb06f5c894be97c6af3802f09b9890fce8e0956cc0e680af4eafd", size = 11183 },
    { url = "https://files.pythonhosted.org/packages/f0/5d/2d45351564273c1bd24ffc691d0d932b0cdef5373cc0f0510239b93d5913/pyobjc_framework_securityinterface-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:0232f947b4f906097a5d758305097a8688835a52e0721b75ae3f1180eac30f50", size = 10885 },
    { url = "https://files.pythonhosted.org/packages/ae/80/7b8dce55a83d1f6ed056f6dd5ec0a927ec0e4fbe60eba05ef1816cc0d959/pyobjc_framework_securityinterface-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:2c20bedead75de7bf1f2ceda562755f64c70ee86180ed45480dc9dbc55609a0b", size = 11225 },
]

[[package]]
name = "pyobjc-framework-securityui"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-security" },
]
sdist = { url = "https://files.pythonhosted.org/packages/07/5b/3b5585d56e0bcaba82e0661224bbc7aaf29fba6b10498971dbe08b2b490a/pyobjc_framework_securityui-11.1.tar.gz", hash = "sha256:e80c93e8a56bf89e4c0333047b9f8219752dd6de290681e9e2e2b2e26d69e92d", size = 12179 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/d0/a4/c9fcc42065b6aed73b14b9650c1dc0a4af26a30d418cbc1bab33621b461c/pyobjc_framework_securityui-11.1-py2.py3-none-any.whl", hash = "sha256:3cdb101b03459fcf8e4064b90021d06761003f669181e02f43ff585e6ba2403d", size = 3581 },
]

[[package]]
name = "pyobjc-framework-sensitivecontentanalysis"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://files.pythonhosted.org/packages/56/7b/e28f6b30d99e9d464427a07ada82b33cd3292f310bf478a1824051d066b9/pyobjc_framework_sensitivecontentanalysis-11.1.tar.gz", hash = "sha256:5b310515c7386f7afaf13e4632d7d9590688182bb7b563f8026c304bdf317308", size = 12796 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/3c/63/76a939ecac74ca079702165330c692ad2c05ff9b2b446a72ddc8cdc63bb9/pyobjc_framework_sensitivecontentanalysis-11.1-py2.py3-none-any.whl", hash = "sha256:dbb78f5917f986a63878bb91263bceba28bd86fc381bad9461cf391646db369f", size = 3852 },
]

[[package]]
name = "pyobjc-framework-servicemanagement"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/20/c6/32e11599d9d232311607b79eb2d1d21c52eaaf001599ea85f8771a933fa2/pyobjc_framework_servicemanagement-11.1.tar.gz", hash = "sha256:90a07164da49338480e0e135b445acc6ae7c08549a2037d1e512d2605fedd80a", size = 16645 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/b9/f1/222462f5afcb6cb3c1fc9e6092dfcffcc7eb9db8bd2cef8c1743a22fbe95/pyobjc_framework_servicemanagement-11.1-py2.py3-none-any.whl", hash = "sha256:104f56557342a05ad68cd0c9daf63b7f4678957fe1f919f03a872f1607a50710", size = 5338 },
]

[[package]]
name = "pyobjc-framework-sharedwithyou"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-sharedwithyoucore" },
]
sdist = { url = "https://files.pythonhosted.org/packages/fe/a5/e299fbd0c13d4fac9356459f21372f6eef4279d0fbc99ba316d88dfbbfb4/pyobjc_framework_sharedwithyou-11.1.tar.gz", hash = "sha256:ece3a28a3083d0bcad0ac95b01f0eb699b9d2d0c02c61305bfd402678753ff6e", size = 34216 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/57/44/211e1f18676e85d3656671fc0c954ced2cd007e55f1b0b6b2e4d0a0852eb/pyobjc_framework_sharedwithyou-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:99e1749187ae370be7b9c55dd076d1b8143f0d8db3e83f52540586f32e7abb33", size = 8740 },
    { url = "https://files.pythonhosted.org/packages/6f/da/1a2f2ae024e0206e1bcaba27aac2ebadf8bceb0ee05d03be2250e8c3d1a3/pyobjc_framework_sharedwithyou-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:c1a1770aa2c417f17010623414fb12943570baa726d8780dd7446ba5bcee8c3d", size = 8759 },
    { url = "https://files.pythonhosted.org/packages/48/85/d54efa902f5dd18a99478eb4fd0befda07dcd2672b1c3ed00ec88280fed0/pyobjc_framework_sharedwithyou-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:63b1cb673b844ebfeddc032d0539f913bbd6b67ab2a310a1fcff7842dba9c714", size = 8909 },
    { url = "https://files.pythonhosted.org/packages/df/a0/03d0277bae4b49f9ec6dd078c7b66ffbeca71ffe47c206222697a7a563e2/pyobjc_framework_sharedwithyou-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:481362f0bde6def86634fc687abe6f4dee650c09c22b48bfe5af5322f9947cef", size = 8807 },
    { url = "https://files.pythonhosted.org/packages/f0/66/0873bad696dfa6f8b597c9de5b0a1e1529f4ed21bf54c8389ec43499298d/pyobjc_framework_sharedwithyou-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:70421a8fd326afd99eeae273b693a7b4d2d200c38e883d8219a84123a4ba0861", size = 8955 },
]

[[package]]
name = "pyobjc-framework-sharedwithyoucore"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/79/a3/1ca6ff1b785772c7c5a38a7c017c6f971b1eda638d6a0aab3bbde18ac086/pyobjc_framework_sharedwithyoucore-11.1.tar.gz", hash = "sha256:790050d25f47bda662a9f008b17ca640ac2460f2559a56b17995e53f2f44ed73", size = 29459 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/b9/70/3b2e13fcf393aa434b1cf5c29c6aaf65ee5b8361254df3a920ed436bb5e4/pyobjc_framework_sharedwithyoucore-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:dd18c588b29de322c25821934d6aa6d2bbbdbb89b6a4efacdb248b4115fc488d", size = 8512 },
    { url = "https://files.pythonhosted.org/packages/b7/fc/feb2912fb9c7bbeb2099d2cb42ad28055c6e29504fcb92bd8a011fcba66a/pyobjc_framework_sharedwithyoucore-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:a3fb0e745fd022fed48cc9a5e0dcbf8d1abcb5bfc192150e3a2584f4351791fc", size = 8527 },
    { url = "https://files.pythonhosted.org/packages/f1/3f/0a8aa5d1b0eb07508c42e900d82a89e096b79fcafcd55e966d4d45476ae5/pyobjc_framework_sharedwithyoucore-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:6aee3df8bed97a74e1f79609f9884edcaab2d305db20bdcae39e47b3e513c559", size = 8672 },
    { url = "https://files.pythonhosted.org/packages/64/f4/582ca62f3b154a5a0c46854c329aae07dddeadbced077394211644d4862b/pyobjc_framework_sharedwithyoucore-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:5a45c562c99017f8e057d4080012b63a9bb660c696334707c54d7b4018ca1017", size = 8569 },
    { url = "https://files.pythonhosted.org/packages/98/3a/b64eccedc362d0427cd67dfa4531b3eb935a2c31419f3f5803f40dcb0803/pyobjc_framework_sharedwithyoucore-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:4e19bfc74f392546ca4b7ea5271d4802617445ad493428370eafd3cddd4d977e", size = 8719 },
]

[[package]]
name = "pyobjc-framework-shazamkit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/de/08/ba739b97f1e441653bae8da5dd1e441bbbfa43940018d21edb60da7dd163/pyobjc_framework_shazamkit-11.1.tar.gz", hash = "sha256:c6e3c9ab8744d9319a89b78ae6f185bb5704efb68509e66d77bcd1f84a9446d6", size = 25797 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/89/b7/594b8bdc406603a7a07cdb33f2be483fed16aebc35aeb087385fc9eca844/pyobjc_framework_shazamkit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:b323f5409b01711aa2b6e2113306084fab2cc83fa57a0c3d55bd5876358b68d8", size = 8560 },
    { url = "https://files.pythonhosted.org/packages/8c/fa/49ba8d1f9e257a12267773d6682e170fba441c7ea72d6fe58da9f4bf6f10/pyobjc_framework_shazamkit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:8bac17f285742e0f13a54c7085ef3035d8034ffc43d18d3d68fb41283c5064ff", size = 8573 },
    { url = "https://files.pythonhosted.org/packages/22/47/eeae6a31a41cbaf29081145b8f54ddebf68a5eba19626dd9ba2c00fdc92b/pyobjc_framework_shazamkit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:b3304c3a67e3722b895d874f215dd4277b49cedddb72fa780a791ef79e5c3d45", size = 8726 },
    { url = "https://files.pythonhosted.org/packages/b9/72/e4e4bca07808f0a930955ddfdd10cf6322096fced76bf06b52d379df850c/pyobjc_framework_shazamkit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:ef51f461672234076b3791ad4be05adad20a2e24b9d7d93acd7bf18d7f9b1714", size = 8610 },
    { url = "https://files.pythonhosted.org/packages/c4/f2/31e186b99ccf22cbceddea58edfdcbef6a336c12326e198e7c6fd18b5938/pyobjc_framework_shazamkit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:f7d191fb187dbb05e3f88f546d5207618d65e270d7a4316b51b1171cc491e268", size = 8766 },
]

[[package]]
name = "pyobjc-framework-social"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/07/2e/cc7707b7a40df392c579087947049f3e1f0e00597e7151ec411f654d8bef/pyobjc_framework_social-11.1.tar.gz", hash = "sha256:fbc09d7b00dad45b547f9b2329f4dcee3f5a50e2348de1870de0bd7be853a5b7", size = 14540 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/86/1d/e1026c082a66075dbb7e57983c0aaaed3ee09f06c346743e8af24d1dc21a/pyobjc_framework_social-11.1-py2.py3-none-any.whl", hash = "sha256:ab5878c47d7a0639704c191cee43eeb259e09688808f0905c42551b9f79e1d57", size = 4444 },
]

[[package]]
name = "pyobjc-framework-soundanalysis"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/e0/d4/b9497dbb57afdf0d22f61bb6e776a6f46cf9294c890448acde5b46dd61f3/pyobjc_framework_soundanalysis-11.1.tar.gz", hash = "sha256:42cd25b7e0f343d8b59367f72b5dae96cf65696bdb8eeead8d7424ed37aa1434", size = 16539 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/13/b4/7e8cf3a02e615239568fdf12497233bbd5b58082615cd28a0c7cd4636309/pyobjc_framework_soundanalysis-11.1-py2.py3-none-any.whl", hash = "sha256:6cf983c24fb2ad2aa5e7499ab2d30ff134d887fe91fd2641acf7472e546ab4e5", size = 4161 },
]

[[package]]
name = "pyobjc-framework-speech"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/67/76/2a1fd7637b2c662349ede09806e159306afeebfba18fb062ad053b41d811/pyobjc_framework_speech-11.1.tar.gz", hash = "sha256:d382977208c3710eacea89e05eae4578f1638bb5a7b667c06971e3d34e96845c", size = 41179 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/78/59/267f4699055beb39723ccbff70909ec3851e4adf17386f6ad85e5d983780/pyobjc_framework_speech-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:7726eff52cfa9cc7178ddcd1285cbc23b5f89ee55b4b850b0d2e90bb4f8e044b", size = 9180 },
    { url = "https://files.pythonhosted.org/packages/ea/a6/c394c3973c42d86c7b0c5c673c5ce65d10671e59e174f1ba4e7ab61ae5df/pyobjc_framework_speech-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:3c80670dbad921bf1d4954a9de29525acb53ee84e064a95fbbdfddff1db2f14f", size = 9198 },
    { url = "https://files.pythonhosted.org/packages/95/e9/3e47e2e3337080e45dd9153c7f465d16c40ce74b11ac53c4663554dab0bd/pyobjc_framework_speech-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:f19778a4ace37c538a34a10ac1f595c80b83489210e6fa60c703399aee264c7e", size = 9355 },
    { url = "https://files.pythonhosted.org/packages/b1/81/dfc795916cfb5d9eb98809e93b380948422d3901ce60ec168681530b6fd5/pyobjc_framework_speech-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:f36ca8a3cfc12b7a5cdf00712eec3ad0fac34e3da36b5737c5302e224525aa70", size = 9249 },
    { url = "https://files.pythonhosted.org/packages/e0/cd/29d5a50d9c596eef5d9b9c1442169908e99bc79edc58b573e393829b1f6b/pyobjc_framework_speech-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:80e577e3dfc1c10a1280deae172cdb64e105f99f47343099e3968b720a3f68da", size = 9401 },
]

[[package]]
name = "pyobjc-framework-spritekit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://files.pythonhosted.org/packages/16/02/2e253ba4f7fad6efe05fd5fcf44aede093f6c438d608d67c6c6623a1846d/pyobjc_framework_spritekit-11.1.tar.gz", hash = "sha256:914da6e846573cac8db5e403dec9a3e6f6edf5211f9b7e429734924d00f65108", size = 130297 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/f1/fe/39d92bf40ec7a6116f89fd95053321f7c00c50c10d82b9adfa0f9ebdb10c/pyobjc_framework_spritekit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:8b470a890db69e70ef428dfff88da499500fca9b2d44da7120dc588d13a2dbdb", size = 17776 },
    { url = "https://files.pythonhosted.org/packages/3f/c1/56490cce24e34e8c4c8c6a0f4746cd3a8bb5c2403e243c99f4dfa0cd147f/pyobjc_framework_spritekit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:2277e74d7be426181ae5ca7dd9d6c776426e8e825ad83b6046a7cb999015f27d", size = 17798 },
    { url = "https://files.pythonhosted.org/packages/75/dc/2ddd3aec417ebb92fd37f687c3e41e051d5e8b761bf2af63b1eb21e20cf4/pyobjc_framework_spritekit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:d6ea27fc202b40945729db50fdc6f75a0a11a07149febf4b99e14caf96ef33b0", size = 18068 },
    { url = "https://files.pythonhosted.org/packages/f1/db/f26835b6c4e169bb451878973e109deb5c8e14c41042d97795200f4d3bbb/pyobjc_framework_spritekit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:e04d0825109a0158e551e9e2a61c56e83eadfdc5a44a47b64cb410b0498d33be", size = 17835 },
    { url = "https://files.pythonhosted.org/packages/4c/c3/e920aacda0bf97b37396eafb93676f359a8407a8e04fae6f9c80c25ba922/pyobjc_framework_spritekit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:4e3673196b7cbc007e4aa7f14d711f3cda00e32e120bc4f6e896d54edd517c61", size = 18092 },
]

[[package]]
name = "pyobjc-framework-storekit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/44/a0/58cab9ebc9ac9282e1d4734b1987d1c3cd652b415ec3e678fcc5e735d279/pyobjc_framework_storekit-11.1.tar.gz", hash = "sha256:85acc30c0bfa120b37c3c5ac693fe9ad2c2e351ee7a1f9ea6f976b0c311ff164", size = 76421 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/ac/61/6404aac6857ea43798882333bcc26bfd3c9c3a1efc7a575cbf3e53538e2a/pyobjc_framework_storekit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:5ca3373272b6989917c88571ca170ce6d771180fe1a2b44c7643fe084569b93e", size = 11868 },
    { url = "https://files.pythonhosted.org/packages/6b/52/23acdf128a5b04059b2a3b38928afbff0afb50da439b597e25cdff1e9148/pyobjc_framework_storekit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:2e2607116b0d53d7fda2fc48e37b1deb1d26a60e7b723a6b7c391a3f48b2ac3b", size = 11882 },
    { url = "https://files.pythonhosted.org/packages/48/04/e7407f5c11a56c9a3a6b4328ec95dbf01ea6f88ac0ff5dc5089e9c8d0a61/pyobjc_framework_storekit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:4944bd1fd01f486623453b68accf4445d3c5686714820c8329a0c4e4672d6fff", size = 12129 },
    { url = "https://files.pythonhosted.org/packages/7a/de/8910a6f54647c0adc2aeb6846afc94a99d17470dd3d905e8b1caeccfcd98/pyobjc_framework_storekit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:d312c392962e15fc842d11b0f7d937e3bd9f3ed3a80f7a6be77518475564f04d", size = 11939 },
    { url = "https://files.pythonhosted.org/packages/b4/12/c04fa481f7ec80beaff532734dde19303133547ae16414934d05d0df046f/pyobjc_framework_storekit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:be6c894a9f9c2b40e300005c3a3cf46f352e1711f65c0b7a8dd5035d1f6333aa", size = 12121 },
]

[[package]]
name = "pyobjc-framework-symbols"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/cd/af/7191276204bd3e7db1d0a3e490a869956606f77f7a303a04d92a5d0c3f7b/pyobjc_framework_symbols-11.1.tar.gz", hash = "sha256:0e09b7813ef2ebdca7567d3179807444dd60f3f393202b35b755d4e1baf99982", size = 13377 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/9a/6a/c91f64ef9b8cd20245b88e392c66cb2279c511724f4ea2983d92584d6f3e/pyobjc_framework_symbols-11.1-py2.py3-none-any.whl", hash = "sha256:1de6fc3af15fc8d5fd4869663a3250311844ec33e99ec8a1991a352ab61d641d", size = 3312 },
]

[[package]]
name = "pyobjc-framework-syncservices"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-coredata" },
]
sdist = { url = "https://files.pythonhosted.org/packages/69/45/cd9fa83ed1d75be7130fb8e41c375f05b5d6621737ec37e9d8da78676613/pyobjc_framework_syncservices-11.1.tar.gz", hash = "sha256:0f141d717256b98c17ec2eddbc983c4bd39dfa00dc0c31b4174742e73a8447fe", size = 57996 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/01/2b/6d7d65c08a9c51eed12eb7f83eaa48deaed621036f77221b3b0346c3f6c2/pyobjc_framework_syncservices-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:03124c8c7c7ce837f51e1c9bdcf84c6f1d5201f92c8a1c172ec34908d5e57415", size = 13496 },
    { url = "https://files.pythonhosted.org/packages/99/7b/88e89b81b5a6ee7da3b452c1619ec22936a8dd4384afd67f6019472655b8/pyobjc_framework_syncservices-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:711d493c7967682bee605c5909a49d268d9b3dd3cb7a71d8ab5dbe01a069eb44", size = 13511 },
    { url = "https://files.pythonhosted.org/packages/bf/3c/6056913cea9fce52f77649b81c54c6282f2eb1b26e7ca17c5c1015123375/pyobjc_framework_syncservices-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:a0ff222472b2cb5c345c92ae4bde245f4181843379f4fd9462cd5c096ed7b2f1", size = 13681 },
    { url = "https://files.pythonhosted.org/packages/63/b1/c9f74441515efd2b05b797df09fff37b61aa583dac6462152063ab47b80d/pyobjc_framework_syncservices-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:24c2b62e94d9e0e5e64abbf6d1f9994212b2a5cb8cad5a8d0394d694b20731b5", size = 13576 },
    { url = "https://files.pythonhosted.org/packages/36/0f/812a2151539aa46363fe4abaad99344380a5c2287840c98a5a021bf3ed0f/pyobjc_framework_syncservices-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:e5b29d6e8fe5b0015dcac5485e4fe6ede35bae7beeb647fb81d86120365029ea", size = 13754 },
]

[[package]]
name = "pyobjc-framework-systemconfiguration"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/e2/3d/41590c0afc72e93d911348fbde0c9c1071ff53c6f86df42df64b21174bb9/pyobjc_framework_systemconfiguration-11.1.tar.gz", hash = "sha256:f30ed0e9a8233fecb06522e67795918ab230ddcc4a18e15494eff7532f4c3ae1", size = 143410 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/b9/61/0e9841bf1c7597f380a6dcefcc9335b6a909f20d9bdf07910cddc8552b42/pyobjc_framework_systemconfiguration-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:6881929b828a566bf1349f09db4943e96a2b33f42556e1f7f6f28b192420f6fc", size = 21639 },
    { url = "https://files.pythonhosted.org/packages/1c/eb/4480a1ab5baba4b9e75bb7f4f667073db5702cf521ddc99941575167585d/pyobjc_framework_systemconfiguration-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:ab2ff52e4228f42182b7ef398d0da504f9f8f4a889963422af9aa1f495668db2", size = 21646 },
    { url = "https://files.pythonhosted.org/packages/b7/00/40d433a160c4d3c156008d375aa0279f46343c69cecb464e59ab1a0b3063/pyobjc_framework_systemconfiguration-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:c236f19cadc9fff56c0afb3e4ad6f8c8e11c5679e31ed413fe6876bf2ea73353", size = 22059 },
    { url = "https://files.pythonhosted.org/packages/60/d0/18ad65359d0fd71c67f14b02bf03efdd6e472185204c82f5885343798d52/pyobjc_framework_systemconfiguration-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:ef266e9f83c2fc9a999709626138b427ff052a0acf4851d797c3a7654878c046", size = 21667 },
    { url = "https://files.pythonhosted.org/packages/e6/cf/4dcf61dd20bfa8d95e4328f431b59119bc2118da9dc570738428ec556b80/pyobjc_framework_systemconfiguration-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:b994c613b5bea9f1c9a64f57f373563c7f424ffae5e4cb20e76c8448a35543f7", size = 22056 },
]

[[package]]
name = "pyobjc-framework-systemextensions"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/b4/57/4609fd9183383616b1e643c2489ad774335f679523a974b9ce346a6d4d5b/pyobjc_framework_systemextensions-11.1.tar.gz", hash = "sha256:8ff9f0aad14dcdd07dd47545c1dd20df7a286306967b0a0232c81fcc382babe6", size = 23062 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/76/40/d9be444b39ec12d68b5e4f712b71d6c00d654936ff5744ea380c1bfabf06/pyobjc_framework_systemextensions-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:3a2b1e84e4a118bfe13efb9f2888b065dc937e2a7e60afd4d0a82b51b8301a10", size = 9130 },
    { url = "https://files.pythonhosted.org/packages/7d/23/f615d69b3a86e75af234149fc12c8dfde8f346148e4eb185696a9c87e824/pyobjc_framework_systemextensions-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:2ed65857244f18b88107e5d3ea8ea21c9da662490895b430e376423ee7c0b963", size = 9154 },
    { url = "https://files.pythonhosted.org/packages/3c/08/2719c95d57f404d880c80da4250ff122ff318307e7a9b8ceef54d56fdb7f/pyobjc_framework_systemextensions-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:9aa7595de4f8f6a252c50419c0343f7326c6a4de47da5b933a17880d1cadfa36", size = 9315 },
    { url = "https://files.pythonhosted.org/packages/88/ff/a984a96f49b27d9c79ab97aa484bac27d3b4f1de14b9a1080de3622e63f1/pyobjc_framework_systemextensions-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:97c1b5f415f3981d0426516e014e94392f054f3898252bf6c88c3f50700c1d70", size = 9204 },
    { url = "https://files.pythonhosted.org/packages/d9/57/574b1c59afac30e605c476c5911a69e70d338adf5ff810042f5d55e77871/pyobjc_framework_systemextensions-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:1801413066d1cbf2a0319e228060820c51ea0fb27aec339716d8c82f2e1b3125", size = 9366 },
]

[[package]]
name = "pyobjc-framework-threadnetwork"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/e7/a4/5400a222ced0e4f077a8f4dd0188e08e2af4762e72ed0ed39f9d27feefc9/pyobjc_framework_threadnetwork-11.1.tar.gz", hash = "sha256:73a32782f44b61ca0f8a4a9811c36b1ca1cdcf96c8a3ba4de35d8e8e58a86ad5", size = 13572 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/b0/f0/b7a577d00bdb561efef82b046a75f627a60de53566ab2d9e9ddd5bd11b66/pyobjc_framework_threadnetwork-11.1-py2.py3-none-any.whl", hash = "sha256:55021455215a0d3ad4e40152f94154e29062e73655558c5f6e71ab097d90083e", size = 3751 },
]

[[package]]
name = "pyobjc-framework-uniformtypeidentifiers"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/c5/4f/066ed1c69352ccc29165f45afb302f8c9c2b5c6f33ee3abfa41b873c07e5/pyobjc_framework_uniformtypeidentifiers-11.1.tar.gz", hash = "sha256:86c499bec8953aeb0c95af39b63f2592832384f09f12523405650b5d5f1ed5e9", size = 20599 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/de/3b/b63b8137dd9f455d5abece6702c06c6b613fac6fda1319aaa2f79d00c380/pyobjc_framework_uniformtypeidentifiers-11.1-py2.py3-none-any.whl", hash = "sha256:6e2e8ea89eb8ca03bc2bc8e506fff901e71d916276475c8d81fbf0280059cb4c", size = 4891 },
]

[[package]]
name = "pyobjc-framework-usernotifications"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/b4/4c/e7e180fcd06c246c37f218bcb01c40ea0213fde5ace3c09d359e60dcaafd/pyobjc_framework_usernotifications-11.1.tar.gz", hash = "sha256:38fc763afa7854b41ddfca8803f679a7305d278af8a7ad02044adc1265699996", size = 55428 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/03/af/a54e343a7226dc65a65f7a561c060f8c96cb9f92f41ce2242d20d82ae594/pyobjc_framework_usernotifications-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:ce6006989fd4a59ec355f6797ccdc9946014ea5241ff7875854799934dbba901", size = 9606 },
    { url = "https://files.pythonhosted.org/packages/d1/fb/ae1ea7f7c511714c1502fa9c4856c6b3dfe110ff7cc094070fec5ad496b8/pyobjc_framework_usernotifications-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:9efa3004059a8fe3f3c52f638f0401dbcdbc7b2f539587c8868da2486a64d674", size = 9628 },
    { url = "https://files.pythonhosted.org/packages/e5/46/4934930848d74aeea32435378154501fcb3dbd77f759c4aa09b99e094310/pyobjc_framework_usernotifications-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:62a4bd242b761a6f00a4374a369391346d225d68be07691e042ec7db452084c8", size = 9793 },
    { url = "https://files.pythonhosted.org/packages/f2/f7/fadd62a479322bc8bf20684c6a87a1eb40b28c03899a8cc3d5b6fe781d93/pyobjc_framework_usernotifications-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:dcdcb657d2fa47108e4ef93ec3320025576857e8f69a15f082f5eda930b35e86", size = 9666 },
    { url = "https://files.pythonhosted.org/packages/72/c3/406d196d094cf8c30bbc815a8ca8ef57bfa21c2494f93ff1125f78f8a922/pyobjc_framework_usernotifications-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:bad5e650c014757159523466e5b2c127e066045e2a5579a5cac9aeca46bda017", size = 9852 },
]

[[package]]
name = "pyobjc-framework-usernotificationsui"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-usernotifications" },
]
sdist = { url = "https://files.pythonhosted.org/packages/d2/c4/03d97bd3adcee9b857533cb42967df0d019f6a034adcdbcfca2569d415b2/pyobjc_framework_usernotificationsui-11.1.tar.gz", hash = "sha256:18e0182bddd10381884530d6a28634ebb3280912592f8f2ad5bac2a9308c6a65", size = 14123 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/9d/2c/0bb489b5ac4daf83b113018701ce30a0cb4bf47c615c92c5844a16e0a012/pyobjc_framework_usernotificationsui-11.1-py2.py3-none-any.whl", hash = "sha256:b84d73d90ab319acf8fad5c59b7a5e2b6023fbb2efd68c58b532e3b3b52f647a", size = 3914 },
]

[[package]]
name = "pyobjc-framework-videosubscriberaccount"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/aa/00/cd9d93d06204bbb7fe68fb97022b0dd4ecdf8af3adb6d70a41e22c860d55/pyobjc_framework_videosubscriberaccount-11.1.tar.gz", hash = "sha256:2dd78586260fcee51044e129197e8bf2e157176e02babeec2f873afa4235d8c6", size = 28856 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/4b/dc/b409dee6dd58a5db2e9a681bde8894c9715468689f18e040f7d252794c3d/pyobjc_framework_videosubscriberaccount-11.1-py2.py3-none-any.whl", hash = "sha256:d5a95ae9f2a6f0180a5bbb10e76c064f0fd327aae00a2fe90aa7b65ed4dad7ef", size = 4695 },
]

[[package]]
name = "pyobjc-framework-videotoolbox"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-coremedia" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://files.pythonhosted.org/packages/e5/e3/df9096f54ae1f27cab8f922ee70cbda5d80f8c1d12734c38580829858133/pyobjc_framework_videotoolbox-11.1.tar.gz", hash = "sha256:a27985656e1b639cdb102fcc727ebc39f71bb1a44cdb751c8c80cc9fe938f3a9", size = 88551 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/1f/cf/569babadbf1f9598f62c400ee02da19d4ab5f36276978c81080999399df9/pyobjc_framework_videotoolbox-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:c55285c3c78183fd2a092d582e30b562777a82985cccca9e7e99a0aff2601591", size = 17432 },
    { url = "https://files.pythonhosted.org/packages/b1/32/1a3d1a448d3cbcaf5c2a4ceaaad32817df21739099e187bbe6e3fd03d6fd/pyobjc_framework_videotoolbox-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:65a96385e80cb9ad3eab7d1f3156452ff805a925c9ca287ff1491a97cca191ba", size = 17450 },
    { url = "https://files.pythonhosted.org/packages/64/d9/530b561bea7b8690ca976570466e42fa226fc60fe3fef3d14beaf719dc99/pyobjc_framework_videotoolbox-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:e282cb07f6a51647ac19a3b5d31e26f1619285bac24171e403921d671e4756d9", size = 17668 },
    { url = "https://files.pythonhosted.org/packages/21/de/478ead66538d665860bfc8fdb7c66a93bc07a9b32bd4150ee181bd16a66b/pyobjc_framework_videotoolbox-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:31acfb12cea4f0624ecb92e74404f15e2755fbf0a3f4133dc93add44cf4a6a9f", size = 17452 },
    { url = "https://files.pythonhosted.org/packages/6d/32/bd465a698e680f95df87b3948dc4ced5f95dc813a88987355ffee5e1638c/pyobjc_framework_videotoolbox-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:0e54bd6cfcbdda4add24e8e873baab11dfb436633100cc6664f3c068e615a6ff", size = 17645 },
]

[[package]]
name = "pyobjc-framework-virtualization"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/f1/ff/57214e8f42755eeaad516a7e673dae4341b8742005d368ecc22c7a790b0b/pyobjc_framework_virtualization-11.1.tar.gz", hash = "sha256:4221ee5eb669e43a2ff46e04178bec149af2d65205deb5d4db5fa62ea060e022", size = 78633 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/c8/4f/fe1930f4ce2c7d2f4c34bb53adf43f412bc91364e8e4cb450a7c8a6b8b59/pyobjc_framework_virtualization-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:59df6702b3e63200752be7d9c0dc590cb4c3b699c886f9a8634dd224c74b3c3c", size = 13084 },
    { url = "https://files.pythonhosted.org/packages/4f/33/6d9f4177983d8894d217b212c25cbb91004cb1103c865961f03360aff68b/pyobjc_framework_virtualization-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:12a5ef32d2b7a56b675ea34fcb68bb9dddb7cf2c0a5ac5131f35551767bdacf1", size = 13093 },
    { url = "https://files.pythonhosted.org/packages/78/af/b9e1b6fa9afb4a6557e3bc1e7e8409108ecf416db5a8a9c6ef4d25dd16af/pyobjc_framework_virtualization-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:790bd2e42e8c5890319f8c576d5e171f87f95655e6fc55cf19a5f85f9e23558a", size = 13284 },
    { url = "https://files.pythonhosted.org/packages/19/d7/9cadb62789974cb7ff65435e4b000d34cf9ec43e46ec2eb73de1620ab6a0/pyobjc_framework_virtualization-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:5f35d823003a613bde27c2c699a8a7de45dc2bdd2e1121e0c4a337b877dfc64e", size = 13111 },
    { url = "https://files.pythonhosted.org/packages/2c/ee/39e84b673a33a10f518ecf5f7398a6a6864d2f23c79996c36809677678a1/pyobjc_framework_virtualization-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:b2e7ab5204fe80249dd8d031b761cf9c0106d0d5e61d88930e0f334f5060d820", size = 13299 },
]

[[package]]
name = "pyobjc-framework-vision"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-coreml" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://files.pythonhosted.org/packages/40/a8/7128da4d0a0103cabe58910a7233e2f98d18c590b1d36d4b3efaaedba6b9/pyobjc_framework_vision-11.1.tar.gz", hash = "sha256:26590512ee7758da3056499062a344b8a351b178be66d4b719327884dde4216b", size = 133721 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/a2/b5/54c0227a695557ea3065bc035b20a5c256f6f3b861e095eee1ec4b4d8cee/pyobjc_framework_vision-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:df076c3e3e672887182953efc934c1f9683304737e792ec09a29bfee90d2e26a", size = 16829 },
    { url = "https://files.pythonhosted.org/packages/20/cf/58ace43525ab073b39df9a740e855ebe83ed78f041d619644af3c60d9013/pyobjc_framework_vision-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:1e5617e37dd2a7cff5e69e9aab039ea74b39ccdc528f6c828f2b60c1254e61e5", size = 16852 },
    { url = "https://files.pythonhosted.org/packages/99/c3/4aeaac1d53766125870aadbe3a4a02d4bca373b18753d32281f77e095976/pyobjc_framework_vision-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:dfd148a6df30ac70a9c41dd90a6c8f8c7f339bd9ca6829629a902f272e02b6b4", size = 16993 },
    { url = "https://files.pythonhosted.org/packages/75/29/bd70761b455067f1f0cb90a7c1983152b0e42b1f05ff91aa42c994a3f97d/pyobjc_framework_vision-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:d1f8fdccc6135fdbfd66d8f21240d6c84465cb8e116a8e5b43601aed020051e5", size = 16847 },
    { url = "https://files.pythonhosted.org/packages/23/e1/72d2410377497b04ecd9718d8784a9d31bce36bbce0cb77c4e4fbcce7070/pyobjc_framework_vision-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:d00830c71a30fc893b3c5ee65119c7e5e5a95a16af53b8e56a0e58cff57e3b56", size = 16995 },
]

[[package]]
name = "pyobjc-framework-webkit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/92/04/fb3d0b68994f7e657ef00c1ac5fc1c04ae2fc7ea581d647f5ae1f6739b14/pyobjc_framework_webkit-11.1.tar.gz", hash = "sha256:27e701c7aaf4f24fc7e601a128e2ef14f2773f4ab071b9db7438dc5afb5053ae", size = 717102 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/8a/7e/fa2c18c0c0f9321e5036e54b9da7a196956b531e50fe1a76e7dfdbe8fac2/pyobjc_framework_webkit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:1a6e6f64ca53c4953f17e808ecac11da288d9a6ade738156ba161732a5e0c96a", size = 51464 },
    { url = "https://files.pythonhosted.org/packages/7a/8d/66561d95b00b8e57a9d5725ae34a8d9ca7ebeb776f13add989421ff90279/pyobjc_framework_webkit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:1d01008756c3912b02b7c02f62432467fbee90a93e3b8e31fa351b4ca97c9c98", size = 51495 },
    { url = "https://files.pythonhosted.org/packages/db/c3/e790b518f84ea8dfbe32a9dcb4d8611b532de08057d19f853c1890110938/pyobjc_framework_webkit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:864f9867a2caaeaeb83e5c0fa3dcf78169622233cf93a9a5eeb7012ced3b8076", size = 51985 },
    { url = "https://files.pythonhosted.org/packages/d7/4f/194e3e7c01861a5e46dfe9e1fa28ad01fd07190cb514e41a7dcf1f0b7031/pyobjc_framework_webkit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:13b774d4244734cb77bf3c3648149c163f62acaa105243d7c48bb3fd856b5628", size = 52248 },
    { url = "https://files.pythonhosted.org/packages/31/09/28884e7c10d3a76a76c2c8f55369dd96a90f0283800c68f5c764e1fb8e2e/pyobjc_framework_webkit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:c1c00d549ab1d50e3d7e8f5f71352b999d2c32dc2365c299f317525eb9bff916", size = 52725 },
]

[[package]]
name = "pyqt6"
version = "6.9.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyqt6-qt6" },
    { name = "pyqt6-sip" },
]
sdist = { url = "https://files.pythonhosted.org/packages/32/1b/567f46eb43ca961efd38d7a0b73efb70d7342854f075fd919179fdb2a571/pyqt6-6.9.1.tar.gz", hash = "sha256:50642be03fb40f1c2111a09a1f5a0f79813e039c15e78267e6faaf8a96c1c3a6", size = 1067230 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/18/c4/fc2a69cf3df09b213185ef5a677c3940cd20e7855d29e40061a685b9c6ee/pyqt6-6.9.1-cp39-abi3-macosx_10_14_universal2.whl", hash = "sha256:33c23d28f6608747ecc8bfd04c8795f61631af9db4fb1e6c2a7523ec4cc916d9", size = 59770566 },
    { url = "https://files.pythonhosted.org/packages/d5/78/92f3c46440a83ebe22ae614bd6792e7b052bcb58ff128f677f5662015184/pyqt6-6.9.1-cp39-abi3-manylinux_2_28_x86_64.whl", hash = "sha256:37884df27f774e2e1c0c96fa41e817a222329b80ffc6241725b0dc8c110acb35", size = 37804959 },
    { url = "https://files.pythonhosted.org/packages/5a/5e/e77fa2761d809cd08d724f44af01a4b6ceb0ff9648e43173187b0e4fac4e/pyqt6-6.9.1-cp39-abi3-manylinux_2_39_aarch64.whl", hash = "sha256:055870b703c1a49ca621f8a89e2ec4d848e6c739d39367eb9687af3b056d9aa3", size = 40414608 },
    { url = "https://files.pythonhosted.org/packages/c4/09/69cf80456b6a985e06dd24ed0c2d3451e43567bf2807a5f3a86ef7a74a2e/pyqt6-6.9.1-cp39-abi3-win_amd64.whl", hash = "sha256:15b95bd273bb6288b070ed7a9503d5ff377aa4882dd6d175f07cad28cdb21da0", size = 25717996 },
    { url = "https://files.pythonhosted.org/packages/52/b3/0839d8fd18b86362a4de384740f2f6b6885b5d06fda7720f8a335425e316/pyqt6-6.9.1-cp39-abi3-win_arm64.whl", hash = "sha256:08792c72d130a02e3248a120f0b9bbb4bf4319095f92865bc5b365b00518f53d", size = 25212132 },
]

[[package]]
name = "pyqt6-qt6"
version = "6.9.1"
source = { registry = "https://pypi.org/simple" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/51/40/04f652e714f85ba6b0c24f4ead860f2c5769f9e64737f415524d792d5914/pyqt6_qt6-6.9.1-py3-none-macosx_10_14_x86_64.whl", hash = "sha256:3854c7f83ee4e8c2d91e23ab88b77f90e2ca7ace34fe72f634a446959f2b4d4a", size = 66236777 },
    { url = "https://files.pythonhosted.org/packages/57/31/e4fa40568a59953ce5cf9a5adfbd1be4a806dafd94e39072d3cc0bed5468/pyqt6_qt6-6.9.1-py3-none-macosx_11_0_arm64.whl", hash = "sha256:123e4aeb037c099bb4696a3ea8edcb1d9d62cedd0b2b950556b26024c97f3293", size = 60551574 },
    { url = "https://files.pythonhosted.org/packages/aa/8d/7c8073cbbefe9c103ec8add70f29ffee1db95a3755b429b9f47cd6afa41b/pyqt6_qt6-6.9.1-py3-none-manylinux_2_28_x86_64.whl", hash = "sha256:cc5bd193ebd2d1a3ec66e1eee65bf532d762c239459bce1ecebf56177243e89b", size = 82000130 },
    { url = "https://files.pythonhosted.org/packages/1e/60/a4ab932028b0c15c0501cb52eb1e7f24f4ce2e4c78d46c7cce58a375a88c/pyqt6_qt6-6.9.1-py3-none-manylinux_2_39_aarch64.whl", hash = "sha256:b065af7243d1d450a49470a8185301196a18b1d41085d3ef476eb55bbb225083", size = 80463127 },
    { url = "https://files.pythonhosted.org/packages/e7/85/552710819019a96d39d924071324a474aec54b31c410d7de8ebb398adcc1/pyqt6_qt6-6.9.1-py3-none-win_amd64.whl", hash = "sha256:f9e54c424bc921ecb76792a75d123e4ecfc26b00b0c57dae526f41f1d57951d3", size = 73778423 },
    { url = "https://files.pythonhosted.org/packages/16/b4/70f6b18a4913f2326dcf7acb15c12cc0b91cb3932c2ba3b5728811f22acd/pyqt6_qt6-6.9.1-py3-none-win_arm64.whl", hash = "sha256:432caaedf5570bc8a9b7c75bc6af6a26bf88589536472eca73417ac019f59d41", size = 49617924 },
]

[[package]]
name = "pyqt6-sip"
version = "13.10.2"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/2f/4a/96daf6c2e4f689faae9bd8cebb52754e76522c58a6af9b5ec86a2e8ec8b4/pyqt6_sip-13.10.2.tar.gz", hash = "sha256:464ad156bf526500ce6bd05cac7a82280af6309974d816739b4a9a627156fafe", size = 92548 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/22/5b/1240017e0d59575289ba52b58fd7f95e7ddf0ed2ede95f3f7e2dc845d337/pyqt6_sip-13.10.2-cp312-cp312-macosx_10_9_universal2.whl", hash = "sha256:83e6a56d3e715f748557460600ec342cbd77af89ec89c4f2a68b185fa14ea46c", size = 112199 },
    { url = "https://files.pythonhosted.org/packages/51/11/1fc3bae02a12a3ac8354aa579b56206286e8b5ca9586677b1058c81c2f74/pyqt6_sip-13.10.2-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:4ccf197f8fa410e076936bee28ad9abadb450931d5be5625446fd20e0d8b27a6", size = 322757 },
    { url = "https://files.pythonhosted.org/packages/21/40/de9491213f480a27199690616959a17a0f234962b86aa1dd4ca2584e922d/pyqt6_sip-13.10.2-cp312-cp312-manylinux_2_5_x86_64.manylinux1_x86_64.whl", hash = "sha256:37af463dcce39285e686d49523d376994d8a2508b9acccb7616c4b117c9c4ed7", size = 304251 },
    { url = "https://files.pythonhosted.org/packages/02/21/cc80e03f1052408c62c341e9fe9b81454c94184f4bd8a95d29d2ec86df92/pyqt6_sip-13.10.2-cp312-cp312-win_amd64.whl", hash = "sha256:c7b34a495b92790c70eae690d9e816b53d3b625b45eeed6ae2c0fe24075a237e", size = 53519 },
    { url = "https://files.pythonhosted.org/packages/77/cf/53bd0863252b260a502659cb3124d9c9fe38047df9360e529b437b4ac890/pyqt6_sip-13.10.2-cp312-cp312-win_arm64.whl", hash = "sha256:c80cc059d772c632f5319632f183e7578cd0976b9498682833035b18a3483e92", size = 45349 },
    { url = "https://files.pythonhosted.org/packages/a1/1e/979ea64c98ca26979d8ce11e9a36579e17d22a71f51d7366d6eec3c82c13/pyqt6_sip-13.10.2-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:8b5d06a0eac36038fa8734657d99b5fe92263ae7a0cd0a67be6acfe220a063e1", size = 112227 },
    { url = "https://files.pythonhosted.org/packages/d9/21/84c230048e3bfef4a9209d16e56dcd2ae10590d03a31556ae8b5f1dcc724/pyqt6_sip-13.10.2-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:ad376a6078da37b049fdf9d6637d71b52727e65c4496a80b753ddc8d27526aca", size = 322920 },
    { url = "https://files.pythonhosted.org/packages/b0/1e/c6a28a142f14e735088534cc92951c3f48cccd77cdd4f3b10d7996be420f/pyqt6_sip-13.10.2-cp313-cp313-manylinux_2_5_x86_64.manylinux1_x86_64.whl", hash = "sha256:3dde8024d055f496eba7d44061c5a1ba4eb72fc95e5a9d7a0dbc908317e0888b", size = 303833 },
    { url = "https://files.pythonhosted.org/packages/89/63/e5adf350c1c3123d4865c013f164c5265512fa79f09ad464fb2fdf9f9e61/pyqt6_sip-13.10.2-cp313-cp313-win_amd64.whl", hash = "sha256:0b097eb58b4df936c4a2a88a2f367c8bb5c20ff049a45a7917ad75d698e3b277", size = 53527 },
    { url = "https://files.pythonhosted.org/packages/58/74/2df4195306d050fbf4963fb5636108a66e5afa6dc05fd9e81e51ec96c384/pyqt6_sip-13.10.2-cp313-cp313-win_arm64.whl", hash = "sha256:cc6a1dfdf324efaac6e7b890a608385205e652845c62130de919fd73a6326244", size = 45373 },
]

[[package]]
name = "pytest"
version = "8.4.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "colorama", marker = "sys_platform == 'win32'" },
    { name = "iniconfig" },
    { name = "packaging" },
    { name = "pluggy" },
    { name = "pygments" },
]
sdist = { url = "https://files.pythonhosted.org/packages/08/ba/45911d754e8eba3d5a841a5ce61a65a685ff1798421ac054f85aa8747dfb/pytest-8.4.1.tar.gz", hash = "sha256:7c67fd69174877359ed9371ec3af8a3d2b04741818c51e5e99cc1742251fa93c", size = 1517714 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/29/16/c8a903f4c4dffe7a12843191437d7cd8e32751d5de349d45d3fe69544e87/pytest-8.4.1-py3-none-any.whl", hash = "sha256:539c70ba6fcead8e78eebbf1115e8b589e7565830d7d006a8723f19ac8a0afb7", size = 365474 },
]

[[package]]
name = "pytest-asyncio"
version = "1.1.0"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pytest" },
]
sdist = { url = "https://files.pythonhosted.org/packages/4e/51/f8794af39eeb870e87a8c8068642fc07bce0c854d6865d7dd0f2a9d338c2/pytest_asyncio-1.1.0.tar.gz", hash = "sha256:796aa822981e01b68c12e4827b8697108f7205020f24b5793b3c41555dab68ea", size = 46652 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/c7/9d/bf86eddabf8c6c9cb1ea9a869d6873b46f105a5d292d3a6f7071f5b07935/pytest_asyncio-1.1.0-py3-none-any.whl", hash = "sha256:5fe2d69607b0bd75c656d1211f969cadba035030156745ee09e7d71740e58ecf", size = 15157 },
]

[[package]]
name = "pytest-mock"
version = "3.14.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pytest" },
]
sdist = { url = "https://files.pythonhosted.org/packages/71/28/67172c96ba684058a4d24ffe144d64783d2a270d0af0d9e792737bddc75c/pytest_mock-3.14.1.tar.gz", hash = "sha256:159e9edac4c451ce77a5cdb9fc5d1100708d2dd4ba3c3df572f14097351af80e", size = 33241 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/b2/05/77b60e520511c53d1c1ca75f1930c7dd8e971d0c4379b7f4b3f9644685ba/pytest_mock-3.14.1-py3-none-any.whl", hash = "sha256:178aefcd11307d874b4cd3100344e7e2d888d9791a6a1d9bfe90fbc1b74fd1d0", size = 9923 },
]

[[package]]
name = "python-dateutil"
version = "2.9.0.post0"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "six" },
]
sdist = { url = "https://files.pythonhosted.org/packages/66/c0/0c8b6ad9f17a802ee498c46e004a0eb49bc148f2fd230864601a86dcf6db/python-dateutil-2.9.0.post0.tar.gz", hash = "sha256:37dd54208da7e1cd875388217d5e00ebd4179249f90fb72437e91a35459a0ad3", size = 342432 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/ec/57/56b9bcc3c9c6a792fcbaf139543cee77261f3651ca9da0c93f5c1221264b/python_dateutil-2.9.0.post0-py2.py3-none-any.whl", hash = "sha256:a8b2bc7bffae282281c8140a97d3aa9c14da0b136dfe83f850eea9a5f7470427", size = 229892 },
]

[[package]]
name = "pyyaml"
version = "6.0.2"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/54/ed/79a089b6be93607fa5cdaedf301d7dfb23af5f25c398d5ead2525b063e17/pyyaml-6.0.2.tar.gz", hash = "sha256:d584d9ec91ad65861cc08d42e834324ef890a082e591037abe114850ff7bbc3e", size = 130631 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/86/0c/c581167fc46d6d6d7ddcfb8c843a4de25bdd27e4466938109ca68492292c/PyYAML-6.0.2-cp312-cp312-macosx_10_9_x86_64.whl", hash = "sha256:c70c95198c015b85feafc136515252a261a84561b7b1d51e3384e0655ddf25ab", size = 183873 },
    { url = "https://files.pythonhosted.org/packages/a8/0c/38374f5bb272c051e2a69281d71cba6fdb983413e6758b84482905e29a5d/PyYAML-6.0.2-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:ce826d6ef20b1bc864f0a68340c8b3287705cae2f8b4b1d932177dcc76721725", size = 173302 },
    { url = "https://files.pythonhosted.org/packages/c3/93/9916574aa8c00aa06bbac729972eb1071d002b8e158bd0e83a3b9a20a1f7/PyYAML-6.0.2-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:1f71ea527786de97d1a0cc0eacd1defc0985dcf6b3f17bb77dcfc8c34bec4dc5", size = 739154 },
    { url = "https://files.pythonhosted.org/packages/95/0f/b8938f1cbd09739c6da569d172531567dbcc9789e0029aa070856f123984/PyYAML-6.0.2-cp312-cp312-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:9b22676e8097e9e22e36d6b7bda33190d0d400f345f23d4065d48f4ca7ae0425", size = 766223 },
    { url = "https://files.pythonhosted.org/packages/b9/2b/614b4752f2e127db5cc206abc23a8c19678e92b23c3db30fc86ab731d3bd/PyYAML-6.0.2-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:80bab7bfc629882493af4aa31a4cfa43a4c57c83813253626916b8c7ada83476", size = 767542 },
    { url = "https://files.pythonhosted.org/packages/d4/00/dd137d5bcc7efea1836d6264f049359861cf548469d18da90cd8216cf05f/PyYAML-6.0.2-cp312-cp312-musllinux_1_1_aarch64.whl", hash = "sha256:0833f8694549e586547b576dcfaba4a6b55b9e96098b36cdc7ebefe667dfed48", size = 731164 },
    { url = "https://files.pythonhosted.org/packages/c9/1f/4f998c900485e5c0ef43838363ba4a9723ac0ad73a9dc42068b12aaba4e4/PyYAML-6.0.2-cp312-cp312-musllinux_1_1_x86_64.whl", hash = "sha256:8b9c7197f7cb2738065c481a0461e50ad02f18c78cd75775628afb4d7137fb3b", size = 756611 },
    { url = "https://files.pythonhosted.org/packages/df/d1/f5a275fdb252768b7a11ec63585bc38d0e87c9e05668a139fea92b80634c/PyYAML-6.0.2-cp312-cp312-win32.whl", hash = "sha256:ef6107725bd54b262d6dedcc2af448a266975032bc85ef0172c5f059da6325b4", size = 140591 },
    { url = "https://files.pythonhosted.org/packages/0c/e8/4f648c598b17c3d06e8753d7d13d57542b30d56e6c2dedf9c331ae56312e/PyYAML-6.0.2-cp312-cp312-win_amd64.whl", hash = "sha256:7e7401d0de89a9a855c839bc697c079a4af81cf878373abd7dc625847d25cbd8", size = 156338 },
    { url = "https://files.pythonhosted.org/packages/ef/e3/3af305b830494fa85d95f6d95ef7fa73f2ee1cc8ef5b495c7c3269fb835f/PyYAML-6.0.2-cp313-cp313-macosx_10_13_x86_64.whl", hash = "sha256:efdca5630322a10774e8e98e1af481aad470dd62c3170801852d752aa7a783ba", size = 181309 },
    { url = "https://files.pythonhosted.org/packages/45/9f/3b1c20a0b7a3200524eb0076cc027a970d320bd3a6592873c85c92a08731/PyYAML-6.0.2-cp313-cp313-macosx_11_0_arm64.whl", hash = "sha256:50187695423ffe49e2deacb8cd10510bc361faac997de9efef88badc3bb9e2d1", size = 171679 },
    { url = "https://files.pythonhosted.org/packages/7c/9a/337322f27005c33bcb656c655fa78325b730324c78620e8328ae28b64d0c/PyYAML-6.0.2-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:0ffe8360bab4910ef1b9e87fb812d8bc0a308b0d0eef8c8f44e0254ab3b07133", size = 733428 },
    { url = "https://files.pythonhosted.org/packages/a3/69/864fbe19e6c18ea3cc196cbe5d392175b4cf3d5d0ac1403ec3f2d237ebb5/PyYAML-6.0.2-cp313-cp313-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:17e311b6c678207928d649faa7cb0d7b4c26a0ba73d41e99c4fff6b6c3276484", size = 763361 },
    { url = "https://files.pythonhosted.org/packages/04/24/b7721e4845c2f162d26f50521b825fb061bc0a5afcf9a386840f23ea19fa/PyYAML-6.0.2-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:70b189594dbe54f75ab3a1acec5f1e3faa7e8cf2f1e08d9b561cb41b845f69d5", size = 759523 },
    { url = "https://files.pythonhosted.org/packages/2b/b2/e3234f59ba06559c6ff63c4e10baea10e5e7df868092bf9ab40e5b9c56b6/PyYAML-6.0.2-cp313-cp313-musllinux_1_1_aarch64.whl", hash = "sha256:41e4e3953a79407c794916fa277a82531dd93aad34e29c2a514c2c0c5fe971cc", size = 726660 },
    { url = "https://files.pythonhosted.org/packages/fe/0f/25911a9f080464c59fab9027482f822b86bf0608957a5fcc6eaac85aa515/PyYAML-6.0.2-cp313-cp313-musllinux_1_1_x86_64.whl", hash = "sha256:68ccc6023a3400877818152ad9a1033e3db8625d899c72eacb5a668902e4d652", size = 751597 },
    { url = "https://files.pythonhosted.org/packages/14/0d/e2c3b43bbce3cf6bd97c840b46088a3031085179e596d4929729d8d68270/PyYAML-6.0.2-cp313-cp313-win32.whl", hash = "sha256:bc2fa7c6b47d6bc618dd7fb02ef6fdedb1090ec036abab80d4681424b84c1183", size = 140527 },
    { url = "https://files.pythonhosted.org/packages/fa/de/02b54f42487e3d3c6efb3f89428677074ca7bf43aae402517bc7cca949f3/PyYAML-6.0.2-cp313-cp313-win_amd64.whl", hash = "sha256:8388ee1976c416731879ac16da0aff3f63b286ffdd57cdeb95f3f2e085687563", size = 156446 },
]

[[package]]
name = "requests"
version = "2.32.4"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "certifi" },
    { name = "charset-normalizer" },
    { name = "idna" },
    { name = "urllib3" },
]
sdist = { url = "https://files.pythonhosted.org/packages/e1/0a/929373653770d8a0d7ea76c37de6e41f11eb07559b103b1c02cafb3f7cf8/requests-2.32.4.tar.gz", hash = "sha256:27d0316682c8a29834d3264820024b62a36942083d52caf2f14c0591336d3422", size = 135258 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/7c/e4/56027c4a6b4ae70ca9de302488c5ca95ad4a39e190093d6c1a8ace08341b/requests-2.32.4-py3-none-any.whl", hash = "sha256:27babd3cda2a6d50b30443204ee89830707d396671944c998b5975b031ac2b2c", size = 64847 },
]

[[package]]
name = "rich"
version = "14.1.0"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "markdown-it-py" },
    { name = "pygments" },
]
sdist = { url = "https://files.pythonhosted.org/packages/fe/75/af448d8e52bf1d8fa6a9d089ca6c07ff4453d86c65c145d0a300bb073b9b/rich-14.1.0.tar.gz", hash = "sha256:e497a48b844b0320d45007cdebfeaeed8db2a4f4bcf49f15e455cfc4af11eaa8", size = 224441 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/e3/30/3c4d035596d3cf444529e0b2953ad0466f6049528a879d27534700580395/rich-14.1.0-py3-none-any.whl", hash = "sha256:536f5f1785986d6dbdea3c75205c473f970777b4a0d6c6dd1b696aa05a3fa04f", size = 243368 },
]

[[package]]
name = "shellingham"
version = "1.5.4"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/58/15/8b3609fd3830ef7b27b655beb4b4e9c62313a4e8da8c676e142cc210d58e/shellingham-1.5.4.tar.gz", hash = "sha256:8dbca0739d487e5bd35ab3ca4b36e11c4078f3a234bfce294b0a0291363404de", size = 10310 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/e0/f9/0595336914c5619e5f28a1fb793285925a8cd4b432c9da0a987836c7f822/shellingham-1.5.4-py2.py3-none-any.whl", hash = "sha256:7ecfff8f2fd72616f7481040475a65b2bf8af90a56c89140852d1120324e8686", size = 9755 },
]

[[package]]
name = "six"
version = "1.17.0"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/94/e7/b2c673351809dca68a0e064b6af791aa332cf192da575fd474ed7d6f16a2/six-1.17.0.tar.gz", hash = "sha256:ff70335d468e7eb6ec65b95b99d3a2836546063f63acc5171de367e834932a81", size = 34031 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/b7/ce/149a00dd41f10bc29e5921b496af8b574d8413afcd5e30dfa0ed46c2cc5e/six-1.17.0-py2.py3-none-any.whl", hash = "sha256:4721f391ed90541fddacab5acf947aa0d3dc7d27b2e1e8eda2be8970586c3274", size = 11050 },
]

[[package]]
name = "sniffio"
version = "1.3.1"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/a2/87/a6771e1546d97e7e041b6ae58d80074f81b7d5121207425c964ddf5cfdbd/sniffio-1.3.1.tar.gz", hash = "sha256:f4324edc670a0f49750a81b895f35c3adb843cca46f0530f79fc1babb23789dc", size = 20372 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/e9/44/75a9c9421471a6c4805dbf2356f7c181a29c1879239abab1ea2cc8f38b40/sniffio-1.3.1-py3-none-any.whl", hash = "sha256:2f6da418d1f1e0fddd844478f41680e794e6051915791a034ff65e5f100525a2", size = 10235 },
]

[[package]]
name = "typer"
version = "0.16.0"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "click" },
    { name = "rich" },
    { name = "shellingham" },
    { name = "typing-extensions" },
]
sdist = { url = "https://files.pythonhosted.org/packages/c5/8c/7d682431efca5fd290017663ea4588bf6f2c6aad085c7f108c5dbc316e70/typer-0.16.0.tar.gz", hash = "sha256:af377ffaee1dbe37ae9440cb4e8f11686ea5ce4e9bae01b84ae7c63b87f1dd3b", size = 102625 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/76/42/3efaf858001d2c2913de7f354563e3a3a2f0decae3efe98427125a8f441e/typer-0.16.0-py3-none-any.whl", hash = "sha256:1f79bed11d4d02d4310e3c1b7ba594183bcedb0ac73b27a9e5f28f6fb5b98855", size = 46317 },
]

[[package]]
name = "typing-extensions"
version = "4.14.1"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/98/5a/da40306b885cc8c09109dc2e1abd358d5684b1425678151cdaed4731c822/typing_extensions-4.14.1.tar.gz", hash = "sha256:38b39f4aeeab64884ce9f74c94263ef78f3c22467c8724005483154c26648d36", size = 107673 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/b5/00/d631e67a838026495268c2f6884f3711a15a9a2a96cd244fdaea53b823fb/typing_extensions-4.14.1-py3-none-any.whl", hash = "sha256:d1e1e3b58374dc93031d6eda2420a48ea44a36c2b4766a4fdeb3710755731d76", size = 43906 },
]

[[package]]
name = "typing-inspection"
version = "0.4.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "typing-extensions" },
]
sdist = { url = "https://files.pythonhosted.org/packages/f8/b1/0c11f5058406b3af7609f121aaa6b609744687f1d158b3c3a5bf4cc94238/typing_inspection-0.4.1.tar.gz", hash = "sha256:6ae134cc0203c33377d43188d4064e9b357dba58cff3185f22924610e70a9d28", size = 75726 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/17/69/cd203477f944c353c31bade965f880aa1061fd6bf05ded0726ca845b6ff7/typing_inspection-0.4.1-py3-none-any.whl", hash = "sha256:389055682238f53b04f7badcb49b989835495a96700ced5dab2d8feae4b26f51", size = 14552 },
]

[[package]]
name = "urllib3"
version = "2.5.0"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/15/22/9ee70a2574a4f4599c47dd506532914ce044817c7752a79b6a51286319bc/urllib3-2.5.0.tar.gz", hash = "sha256:3fc47733c7e419d4bc3f6b3dc2b4f890bb743906a30d56ba4a5bfa4bbff92760", size = 393185 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/a7/c2/fe1e52489ae3122415c51f387e221dd0773709bad6c6cdaa599e8a2c5185/urllib3-2.5.0-py3-none-any.whl", hash = "sha256:e6b01673c0fa6a13e374b50871808eb3bf7046c4b125b216f6bf1cc604cff0dc", size = 129795 },
]

[[package]]
name = "yarl"
version = "1.20.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "idna" },
    { name = "multidict" },
    { name = "propcache" },
]
sdist = { url = "https://files.pythonhosted.org/packages/3c/fb/efaa23fa4e45537b827620f04cf8f3cd658b76642205162e072703a5b963/yarl-1.20.1.tar.gz", hash = "sha256:d017a4997ee50c91fd5466cef416231bb82177b93b029906cefc542ce14c35ac", size = 186428 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/5f/9a/cb7fad7d73c69f296eda6815e4a2c7ed53fc70c2f136479a91c8e5fbdb6d/yarl-1.20.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:bdcc4cd244e58593a4379fe60fdee5ac0331f8eb70320a24d591a3be197b94a9", size = 133667 },
    { url = "https://files.pythonhosted.org/packages/67/38/688577a1cb1e656e3971fb66a3492501c5a5df56d99722e57c98249e5b8a/yarl-1.20.1-cp312-cp312-macosx_10_13_x86_64.whl", hash = "sha256:b29a2c385a5f5b9c7d9347e5812b6f7ab267193c62d282a540b4fc528c8a9d2a", size = 91025 },
    { url = "https://files.pythonhosted.org/packages/50/ec/72991ae51febeb11a42813fc259f0d4c8e0507f2b74b5514618d8b640365/yarl-1.20.1-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:1112ae8154186dfe2de4732197f59c05a83dc814849a5ced892b708033f40dc2", size = 89709 },
    { url = "https://files.pythonhosted.org/packages/99/da/4d798025490e89426e9f976702e5f9482005c548c579bdae792a4c37769e/yarl-1.20.1-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:90bbd29c4fe234233f7fa2b9b121fb63c321830e5d05b45153a2ca68f7d310ee", size = 352287 },
    { url = "https://files.pythonhosted.org/packages/1a/26/54a15c6a567aac1c61b18aa0f4b8aa2e285a52d547d1be8bf48abe2b3991/yarl-1.20.1-cp312-cp312-manylinux_2_17_armv7l.manylinux2014_armv7l.manylinux_2_31_armv7l.whl", hash = "sha256:680e19c7ce3710ac4cd964e90dad99bf9b5029372ba0c7cbfcd55e54d90ea819", size = 345429 },
    { url = "https://files.pythonhosted.org/packages/d6/95/9dcf2386cb875b234353b93ec43e40219e14900e046bf6ac118f94b1e353/yarl-1.20.1-cp312-cp312-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:4a979218c1fdb4246a05efc2cc23859d47c89af463a90b99b7c56094daf25a16", size = 365429 },
    { url = "https://files.pythonhosted.org/packages/91/b2/33a8750f6a4bc224242a635f5f2cff6d6ad5ba651f6edcccf721992c21a0/yarl-1.20.1-cp312-cp312-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:255b468adf57b4a7b65d8aad5b5138dce6a0752c139965711bdcb81bc370e1b6", size = 363862 },
    { url = "https://files.pythonhosted.org/packages/98/28/3ab7acc5b51f4434b181b0cee8f1f4b77a65919700a355fb3617f9488874/yarl-1.20.1-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:a97d67108e79cfe22e2b430d80d7571ae57d19f17cda8bb967057ca8a7bf5bfd", size = 355616 },
    { url = "https://files.pythonhosted.org/packages/36/a3/f666894aa947a371724ec7cd2e5daa78ee8a777b21509b4252dd7bd15e29/yarl-1.20.1-cp312-cp312-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:8570d998db4ddbfb9a590b185a0a33dbf8aafb831d07a5257b4ec9948df9cb0a", size = 339954 },
    { url = "https://files.pythonhosted.org/packages/f1/81/5f466427e09773c04219d3450d7a1256138a010b6c9f0af2d48565e9ad13/yarl-1.20.1-cp312-cp312-musllinux_1_2_aarch64.whl", hash = "sha256:97c75596019baae7c71ccf1d8cc4738bc08134060d0adfcbe5642f778d1dca38", size = 365575 },
    { url = "https://files.pythonhosted.org/packages/2e/e3/e4b0ad8403e97e6c9972dd587388940a032f030ebec196ab81a3b8e94d31/yarl-1.20.1-cp312-cp312-musllinux_1_2_armv7l.whl", hash = "sha256:1c48912653e63aef91ff988c5432832692ac5a1d8f0fb8a33091520b5bbe19ef", size = 365061 },
    { url = "https://files.pythonhosted.org/packages/ac/99/b8a142e79eb86c926f9f06452eb13ecb1bb5713bd01dc0038faf5452e544/yarl-1.20.1-cp312-cp312-musllinux_1_2_i686.whl", hash = "sha256:4c3ae28f3ae1563c50f3d37f064ddb1511ecc1d5584e88c6b7c63cf7702a6d5f", size = 364142 },
    { url = "https://files.pythonhosted.org/packages/34/f2/08ed34a4a506d82a1a3e5bab99ccd930a040f9b6449e9fd050320e45845c/yarl-1.20.1-cp312-cp312-musllinux_1_2_ppc64le.whl", hash = "sha256:c5e9642f27036283550f5f57dc6156c51084b458570b9d0d96100c8bebb186a8", size = 381894 },
    { url = "https://files.pythonhosted.org/packages/92/f8/9a3fbf0968eac704f681726eff595dce9b49c8a25cd92bf83df209668285/yarl-1.20.1-cp312-cp312-musllinux_1_2_s390x.whl", hash = "sha256:2c26b0c49220d5799f7b22c6838409ee9bc58ee5c95361a4d7831f03cc225b5a", size = 383378 },
    { url = "https://files.pythonhosted.org/packages/af/85/9363f77bdfa1e4d690957cd39d192c4cacd1c58965df0470a4905253b54f/yarl-1.20.1-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:564ab3d517e3d01c408c67f2e5247aad4019dcf1969982aba3974b4093279004", size = 374069 },
    { url = "https://files.pythonhosted.org/packages/35/99/9918c8739ba271dcd935400cff8b32e3cd319eaf02fcd023d5dcd487a7c8/yarl-1.20.1-cp312-cp312-win32.whl", hash = "sha256:daea0d313868da1cf2fac6b2d3a25c6e3a9e879483244be38c8e6a41f1d876a5", size = 81249 },
    { url = "https://files.pythonhosted.org/packages/eb/83/5d9092950565481b413b31a23e75dd3418ff0a277d6e0abf3729d4d1ce25/yarl-1.20.1-cp312-cp312-win_amd64.whl", hash = "sha256:48ea7d7f9be0487339828a4de0360d7ce0efc06524a48e1810f945c45b813698", size = 86710 },
    { url = "https://files.pythonhosted.org/packages/8a/e1/2411b6d7f769a07687acee88a062af5833cf1966b7266f3d8dfb3d3dc7d3/yarl-1.20.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:0b5ff0fbb7c9f1b1b5ab53330acbfc5247893069e7716840c8e7d5bb7355038a", size = 131811 },
    { url = "https://files.pythonhosted.org/packages/b2/27/584394e1cb76fb771371770eccad35de400e7b434ce3142c2dd27392c968/yarl-1.20.1-cp313-cp313-macosx_10_13_x86_64.whl", hash = "sha256:14f326acd845c2b2e2eb38fb1346c94f7f3b01a4f5c788f8144f9b630bfff9a3", size = 90078 },
    { url = "https://files.pythonhosted.org/packages/bf/9a/3246ae92d4049099f52d9b0fe3486e3b500e29b7ea872d0f152966fc209d/yarl-1.20.1-cp313-cp313-macosx_11_0_arm64.whl", hash = "sha256:f60e4ad5db23f0b96e49c018596707c3ae89f5d0bd97f0ad3684bcbad899f1e7", size = 88748 },
    { url = "https://files.pythonhosted.org/packages/a3/25/35afe384e31115a1a801fbcf84012d7a066d89035befae7c5d4284df1e03/yarl-1.20.1-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:49bdd1b8e00ce57e68ba51916e4bb04461746e794e7c4d4bbc42ba2f18297691", size = 349595 },
    { url = "https://files.pythonhosted.org/packages/28/2d/8aca6cb2cabc8f12efcb82749b9cefecbccfc7b0384e56cd71058ccee433/yarl-1.20.1-cp313-cp313-manylinux_2_17_armv7l.manylinux2014_armv7l.manylinux_2_31_armv7l.whl", hash = "sha256:66252d780b45189975abfed839616e8fd2dbacbdc262105ad7742c6ae58f3e31", size = 342616 },
    { url = "https://files.pythonhosted.org/packages/0b/e9/1312633d16b31acf0098d30440ca855e3492d66623dafb8e25b03d00c3da/yarl-1.20.1-cp313-cp313-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:59174e7332f5d153d8f7452a102b103e2e74035ad085f404df2e40e663a22b28", size = 361324 },
    { url = "https://files.pythonhosted.org/packages/bc/a0/688cc99463f12f7669eec7c8acc71ef56a1521b99eab7cd3abb75af887b0/yarl-1.20.1-cp313-cp313-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:e3968ec7d92a0c0f9ac34d5ecfd03869ec0cab0697c91a45db3fbbd95fe1b653", size = 359676 },
    { url = "https://files.pythonhosted.org/packages/af/44/46407d7f7a56e9a85a4c207724c9f2c545c060380718eea9088f222ba697/yarl-1.20.1-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:d1a4fbb50e14396ba3d375f68bfe02215d8e7bc3ec49da8341fe3157f59d2ff5", size = 352614 },
    { url = "https://files.pythonhosted.org/packages/b1/91/31163295e82b8d5485d31d9cf7754d973d41915cadce070491778d9c9825/yarl-1.20.1-cp313-cp313-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:11a62c839c3a8eac2410e951301309426f368388ff2f33799052787035793b02", size = 336766 },
    { url = "https://files.pythonhosted.org/packages/b4/8e/c41a5bc482121f51c083c4c2bcd16b9e01e1cf8729e380273a952513a21f/yarl-1.20.1-cp313-cp313-musllinux_1_2_aarch64.whl", hash = "sha256:041eaa14f73ff5a8986b4388ac6bb43a77f2ea09bf1913df7a35d4646db69e53", size = 364615 },
    { url = "https://files.pythonhosted.org/packages/e3/5b/61a3b054238d33d70ea06ebba7e58597891b71c699e247df35cc984ab393/yarl-1.20.1-cp313-cp313-musllinux_1_2_armv7l.whl", hash = "sha256:377fae2fef158e8fd9d60b4c8751387b8d1fb121d3d0b8e9b0be07d1b41e83dc", size = 360982 },
    { url = "https://files.pythonhosted.org/packages/df/a3/6a72fb83f8d478cb201d14927bc8040af901811a88e0ff2da7842dd0ed19/yarl-1.20.1-cp313-cp313-musllinux_1_2_i686.whl", hash = "sha256:1c92f4390e407513f619d49319023664643d3339bd5e5a56a3bebe01bc67ec04", size = 369792 },
    { url = "https://files.pythonhosted.org/packages/7c/af/4cc3c36dfc7c077f8dedb561eb21f69e1e9f2456b91b593882b0b18c19dc/yarl-1.20.1-cp313-cp313-musllinux_1_2_ppc64le.whl", hash = "sha256:d25ddcf954df1754ab0f86bb696af765c5bfaba39b74095f27eececa049ef9a4", size = 382049 },
    { url = "https://files.pythonhosted.org/packages/19/3a/e54e2c4752160115183a66dc9ee75a153f81f3ab2ba4bf79c3c53b33de34/yarl-1.20.1-cp313-cp313-musllinux_1_2_s390x.whl", hash = "sha256:909313577e9619dcff8c31a0ea2aa0a2a828341d92673015456b3ae492e7317b", size = 384774 },
    { url = "https://files.pythonhosted.org/packages/9c/20/200ae86dabfca89060ec6447649f219b4cbd94531e425e50d57e5f5ac330/yarl-1.20.1-cp313-cp313-musllinux_1_2_x86_64.whl", hash = "sha256:793fd0580cb9664548c6b83c63b43c477212c0260891ddf86809e1c06c8b08f1", size = 374252 },
    { url = "https://files.pythonhosted.org/packages/83/75/11ee332f2f516b3d094e89448da73d557687f7d137d5a0f48c40ff211487/yarl-1.20.1-cp313-cp313-win32.whl", hash = "sha256:468f6e40285de5a5b3c44981ca3a319a4b208ccc07d526b20b12aeedcfa654b7", size = 81198 },
    { url = "https://files.pythonhosted.org/packages/ba/ba/39b1ecbf51620b40ab402b0fc817f0ff750f6d92712b44689c2c215be89d/yarl-1.20.1-cp313-cp313-win_amd64.whl", hash = "sha256:495b4ef2fea40596bfc0affe3837411d6aa3371abcf31aac0ccc4bdd64d4ef5c", size = 86346 },
    { url = "https://files.pythonhosted.org/packages/43/c7/669c52519dca4c95153c8ad96dd123c79f354a376346b198f438e56ffeb4/yarl-1.20.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:f60233b98423aab21d249a30eb27c389c14929f47be8430efa7dbd91493a729d", size = 138826 },
    { url = "https://files.pythonhosted.org/packages/6a/42/fc0053719b44f6ad04a75d7f05e0e9674d45ef62f2d9ad2c1163e5c05827/yarl-1.20.1-cp313-cp313t-macosx_10_13_x86_64.whl", hash = "sha256:6f3eff4cc3f03d650d8755c6eefc844edde99d641d0dcf4da3ab27141a5f8ddf", size = 93217 },
    { url = "https://files.pythonhosted.org/packages/4f/7f/fa59c4c27e2a076bba0d959386e26eba77eb52ea4a0aac48e3515c186b4c/yarl-1.20.1-cp313-cp313t-macosx_11_0_arm64.whl", hash = "sha256:69ff8439d8ba832d6bed88af2c2b3445977eba9a4588b787b32945871c2444e3", size = 92700 },
    { url = "https://files.pythonhosted.org/packages/2f/d4/062b2f48e7c93481e88eff97a6312dca15ea200e959f23e96d8ab898c5b8/yarl-1.20.1-cp313-cp313t-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:3cf34efa60eb81dd2645a2e13e00bb98b76c35ab5061a3989c7a70f78c85006d", size = 347644 },
    { url = "https://files.pythonhosted.org/packages/89/47/78b7f40d13c8f62b499cc702fdf69e090455518ae544c00a3bf4afc9fc77/yarl-1.20.1-cp313-cp313t-manylinux_2_17_armv7l.manylinux2014_armv7l.manylinux_2_31_armv7l.whl", hash = "sha256:8e0fe9364ad0fddab2688ce72cb7a8e61ea42eff3c7caeeb83874a5d479c896c", size = 323452 },
    { url = "https://files.pythonhosted.org/packages/eb/2b/490d3b2dc66f52987d4ee0d3090a147ea67732ce6b4d61e362c1846d0d32/yarl-1.20.1-cp313-cp313t-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:8f64fbf81878ba914562c672024089e3401974a39767747691c65080a67b18c1", size = 346378 },
    { url = "https://files.pythonhosted.org/packages/66/ad/775da9c8a94ce925d1537f939a4f17d782efef1f973039d821cbe4bcc211/yarl-1.20.1-cp313-cp313t-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:f6342d643bf9a1de97e512e45e4b9560a043347e779a173250824f8b254bd5ce", size = 353261 },
    { url = "https://files.pythonhosted.org/packages/4b/23/0ed0922b47a4f5c6eb9065d5ff1e459747226ddce5c6a4c111e728c9f701/yarl-1.20.1-cp313-cp313t-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:56dac5f452ed25eef0f6e3c6a066c6ab68971d96a9fb441791cad0efba6140d3", size = 335987 },
    { url = "https://files.pythonhosted.org/packages/3e/49/bc728a7fe7d0e9336e2b78f0958a2d6b288ba89f25a1762407a222bf53c3/yarl-1.20.1-cp313-cp313t-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:c7d7f497126d65e2cad8dc5f97d34c27b19199b6414a40cb36b52f41b79014be", size = 329361 },
    { url = "https://files.pythonhosted.org/packages/93/8f/b811b9d1f617c83c907e7082a76e2b92b655400e61730cd61a1f67178393/yarl-1.20.1-cp313-cp313t-musllinux_1_2_aarch64.whl", hash = "sha256:67e708dfb8e78d8a19169818eeb5c7a80717562de9051bf2413aca8e3696bf16", size = 346460 },
    { url = "https://files.pythonhosted.org/packages/70/fd/af94f04f275f95da2c3b8b5e1d49e3e79f1ed8b6ceb0f1664cbd902773ff/yarl-1.20.1-cp313-cp313t-musllinux_1_2_armv7l.whl", hash = "sha256:595c07bc79af2494365cc96ddeb772f76272364ef7c80fb892ef9d0649586513", size = 334486 },
    { url = "https://files.pythonhosted.org/packages/84/65/04c62e82704e7dd0a9b3f61dbaa8447f8507655fd16c51da0637b39b2910/yarl-1.20.1-cp313-cp313t-musllinux_1_2_i686.whl", hash = "sha256:7bdd2f80f4a7df852ab9ab49484a4dee8030023aa536df41f2d922fd57bf023f", size = 342219 },
    { url = "https://files.pythonhosted.org/packages/91/95/459ca62eb958381b342d94ab9a4b6aec1ddec1f7057c487e926f03c06d30/yarl-1.20.1-cp313-cp313t-musllinux_1_2_ppc64le.whl", hash = "sha256:c03bfebc4ae8d862f853a9757199677ab74ec25424d0ebd68a0027e9c639a390", size = 350693 },
    { url = "https://files.pythonhosted.org/packages/a6/00/d393e82dd955ad20617abc546a8f1aee40534d599ff555ea053d0ec9bf03/yarl-1.20.1-cp313-cp313t-musllinux_1_2_s390x.whl", hash = "sha256:344d1103e9c1523f32a5ed704d576172d2cabed3122ea90b1d4e11fe17c66458", size = 355803 },
    { url = "https://files.pythonhosted.org/packages/9e/ed/c5fb04869b99b717985e244fd93029c7a8e8febdfcffa06093e32d7d44e7/yarl-1.20.1-cp313-cp313t-musllinux_1_2_x86_64.whl", hash = "sha256:88cab98aa4e13e1ade8c141daeedd300a4603b7132819c484841bb7af3edce9e", size = 341709 },
    { url = "https://files.pythonhosted.org/packages/24/fd/725b8e73ac2a50e78a4534ac43c6addf5c1c2d65380dd48a9169cc6739a9/yarl-1.20.1-cp313-cp313t-win32.whl", hash = "sha256:b121ff6a7cbd4abc28985b6028235491941b9fe8fe226e6fdc539c977ea1739d", size = 86591 },
    { url = "https://files.pythonhosted.org/packages/94/c3/b2e9f38bc3e11191981d57ea08cab2166e74ea770024a646617c9cddd9f6/yarl-1.20.1-cp313-cp313t-win_amd64.whl", hash = "sha256:541d050a355bbbc27e55d906bc91cb6fe42f96c01413dd0f4ed5a5240513874f", size = 93003 },
    { url = "https://files.pythonhosted.org/packages/b4/2d/2345fce04cfd4bee161bf1e7d9cdc702e3e16109021035dbb24db654a622/yarl-1.20.1-py3-none-any.whl", hash = "sha256:83b8eb083fe4683c6115795d9fc1cfaf2cbbefb19b3a1cb68f6527460f483a77", size = 46542 },
]
