# ChatGPT Controller API 配置示例
# 复制此文件为 ../config/config.yaml 并修改相应配置

# API 服务器配置
base_url: http://localhost:8000  # API 服务器地址
api_key: your_api_key_here       # API 密钥，用于 Bearer 认证
api_version: v1                  # API 版本
timeout: 30                      # 请求超时时间（秒）

# CLI 配置
cli_output_format: table         # CLI 输出格式 (table, json, yaml)
cli_color: true                  # 启用彩色输出

# GUI 配置
gui_theme: light                 # GUI 主题 (light, dark)
gui_window_size: [1200, 800]     # 默认窗口大小

# 日志配置
log_level: INFO                  # 日志级别 (DEBUG, INFO, WARNING, ERROR)
log_file: null                   # 日志文件路径 (null 表示不写入文件)

# 使用说明:
# 1. 将此文件复制为 ../config/config.yaml
# 2. 修改 base_url 为您的 API 服务器地址
# 3. 修改 api_key 为您的实际 API 密钥
# 4. 根据需要调整其他配置项

# 环境变量支持:
# 您也可以通过环境变量覆盖配置:
# export CHATGPT_BASE_URL=http://your-server:8000
# export CHATGPT_API_KEY=your_actual_api_key
